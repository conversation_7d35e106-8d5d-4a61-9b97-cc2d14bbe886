//
//  AICharacter.m
//  SteVx - AI Steampunk Jewelry Makers
//
//  Created by Dev<PERSON>per on 2024/08/01.
//  Copyright © 2024 SteVx. All rights reserved.
//

#import "AICharacter.h"

@interface AICharacter ()

@property (nonatomic, strong, readwrite) NSString *characterId;
@property (nonatomic, strong, readwrite) NSString *name;
@property (nonatomic, strong, readwrite) NSString *shortDescription;
@property (nonatomic, strong, readwrite) NSString *longDescription;
@property (nonatomic, strong, readwrite) NSString *iconName;
@property (nonatomic, strong, readwrite) NSArray<NSString *> *specialties;
@property (nonatomic, strong, readwrite) NSArray<NSString *> *tools;
@property (nonatomic, strong, readwrite) NSArray<NSString *> *materials;

@end

@implementation AICharacter

- (instancetype)initWithId:(NSString *)characterId
                      name:(NSString *)name
          shortDescription:(NSString *)shortDescription
           longDescription:(NSString *)longDescription
                  iconName:(NSString *)iconName
               specialties:(NSArray<NSString *> *)specialties
                     tools:(NSArray<NSString *> *)tools
                 materials:(NSArray<NSString *> *)materials {
    self = [super init];
    if (self) {
        _characterId = characterId;
        _name = name;
        _shortDescription = shortDescription;
        _longDescription = longDescription;
        _iconName = iconName;
        _specialties = specialties;
        _tools = tools;
        _materials = materials;
    }
    return self;
}

+ (NSArray<AICharacter *> *)getAllCharacters {
    static NSArray<AICharacter *> *characters = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        characters = @[
            [[AICharacter alloc] initWithId:@"gear_craft"
                                       name:@"GearCraft"
                           shortDescription:@"Make steam punk gear-based jewelry."
                            longDescription:@"Use metal gears (brass, copper), attach with jump rings. Add chains or leather cords. Seal with clear lacquer to prevent tarnish."
                                   iconName:@"gear"
                                specialties:@[@"Gear Design", @"Metal Processing", @"Rust Prevention"]
                                      tools:@[@"Jump Rings", @"Chains", @"Leather Cords"]
                                  materials:@[@"Brass Gears", @"Copper Gears", @"Clear Lacquer"]],
            
            [[AICharacter alloc] initWithId:@"badge_punk"
                                       name:@"BadgePunk"
                           shortDescription:@"Create vintage steam punk badges."
                            longDescription:@"Cut metal sheets into shapes, stamp with gears/rivets. Add enamel accents (burgundy, gold). Attach pins; distress edges for aged look."
                                   iconName:@"badge"
                                specialties:@[@"Badge Making", @"Metal Stamping", @"Aging Craft"]
                                      tools:@[@"Metal Sheets", @"Stamping Tools", @"Pins"]
                                  materials:@[@"Metal Plates", @"Enamel Paint", @"Burgundy Paint"]],
            
            [[AICharacter alloc] initWithId:@"goggle_craft"
                                       name:@"GoggleCraft"
                           shortDescription:@"Make mini steam punk goggle accessories."
                            longDescription:@"Use small metal frames, add tinted plastic lenses. Attach leather straps, tiny gears. Seal with antique brass spray."
                                   iconName:@"goggles"
                                specialties:@[@"Goggle Making", @"Lens Processing", @"Leather Craft"]
                                      tools:@[@"Metal Frames", @"Tinted Lenses", @"Leather Straps"]
                                  materials:@[@"Metal Frames", @"Colored Lenses", @"Antique Brass Spray"]],
            
            [[AICharacter alloc] initWithId:@"key_punk"
                                       name:@"KeyPunk"
                           shortDescription:@"Craft steam punk keychains/pendants."
                            longDescription:@"Combine old keys with gears, use wire to wrap. Add beads (amber, black). Seal with wax for a vintage finish."
                                   iconName:@"key"
                                specialties:@[@"Key Modification", @"Wire Wrapping", @"Vintage Processing"]
                                      tools:@[@"Old Keys", @"Metal Wire", @"Beads"]
                                  materials:@[@"Antique Keys", @"Amber Beads", @"Black Beads", @"Wax Sealant"]],
            
            [[AICharacter alloc] initWithId:@"start_punk"
                                       name:@"StartPunk"
                           shortDescription:@"Basics for steam punk jewelry newbies."
                            longDescription:@"Start with pre-made gears, use hot glue for ease. Try simple designs (gear necklaces). Practice wire wrapping with pliers."
                                   iconName:@"beginner"
                                specialties:@[@"Basic Teaching", @"Simple Design", @"Tool Usage"]
                                      tools:@[@"Pre-made Gears", @"Hot Glue", @"Pliers"]
                                  materials:@[@"Ready Gears", @"Glue", @"Basic Wire"]],
            
            [[AICharacter alloc] initWithId:@"leather_punk"
                                       name:@"LeatherPunk"
                           shortDescription:@"Make leather steam punk accessories."
                            longDescription:@"Stamp leather with gears, add metal studs. Dye brown/black, distress edges. Attach buckles or chains."
                                   iconName:@"leather"
                                specialties:@[@"Leather Craft", @"Stamping Tech", @"Metal Decoration"]
                                      tools:@[@"Leather Stamps", @"Metal Studs", @"Buckles"]
                                  materials:@[@"Leather", @"Brown Dye", @"Black Dye"]],
            
            [[AICharacter alloc] initWithId:@"bend_punk"
                                       name:@"BendPunk"
                           shortDescription:@"Shape metal for steam punk pieces."
                            longDescription:@"Use aluminum wire (easy to bend) for frames. Form loops, attach gears. Use pliers for precise curves."
                                   iconName:@"bend"
                                specialties:@[@"Metal Shaping", @"Wire Bending", @"Precision Work"]
                                      tools:@[@"Aluminum Wire", @"Pliers", @"Forming Tools"]
                                  materials:@[@"Aluminum Wire", @"Bending Molds", @"Clamps"]],
            
            [[AICharacter alloc] initWithId:@"ring_punk"
                                       name:@"RingPunk"
                           shortDescription:@"Craft steam punk style rings."
                            longDescription:@"Use adjustable ring bases, glue small gears. Add tiny chains or mini keys. Seal to protect from wear."
                                   iconName:@"ring"
                                specialties:@[@"Ring Making", @"Micro Decoration", @"Wear Protection"]
                                      tools:@[@"Ring Bases", @"Small Gears", @"Mini Keys"]
                                  materials:@[@"Ring Bases", @"Micro Gears", @"Tiny Chains"]],
            
            [[AICharacter alloc] initWithId:@"aged_punk"
                                       name:@"AgedPunk"
                           shortDescription:@"Distress steam punk jewelry for vintage look."
                            longDescription:@"Use sandpaper on metal edges, apply patina solution. Rub with wax to highlight details. Avoid over-distressing."
                                   iconName:@"aged"
                                specialties:@[@"Aging Craft", @"Surface Treatment", @"Detail Enhancement"]
                                      tools:@[@"Sandpaper", @"Patina Solution", @"Wax"]
                                  materials:@[@"Abrasive Materials", @"Aging Solution", @"Protective Wax"]],
            
            [[AICharacter alloc] initWithId:@"mix_punk"
                                       name:@"MixPunk"
                           shortDescription:@"Combine materials for steam punk pieces."
                            longDescription:@"Pair metal (brass) with leather, wood, or glass. Balance textures—smooth metal with rough leather. Keep color schemes warm (bronze, brown)."
                                   iconName:@"mix"
                                specialties:@[@"Material Pairing", @"Texture Balance", @"Color Coordination"]
                                      tools:@[@"Brass", @"Leather", @"Wood", @"Glass"]
                                  materials:@[@"Various Metals", @"Natural Materials", @"Decorative Glass"]]
        ];
    });
    return characters;
}

+ (nullable AICharacter *)getCharacterById:(NSString *)characterId {
    NSArray<AICharacter *> *characters = [self getAllCharacters];
    for (AICharacter *character in characters) {
        if ([character.characterId isEqualToString:characterId]) {
            return character;
        }
    }
    return nil;
}

- (NSString *)getWelcomeMessage {
    return [NSString stringWithFormat:@"Hello! I'm %@. %@", self.name, self.longDescription];
}

- (NSString *)generateResponseToMessage:(NSString *)userMessage {
    NSArray *responses = @[
        [NSString stringWithFormat:@"That's a great question! Regarding %@'s expertise, I suggest you start with %@.", self.name, self.materials.firstObject],
        [NSString stringWithFormat:@"Based on my experience, %@ is key. You can use %@ to achieve this effect.", self.specialties.firstObject, self.tools.firstObject],
        @"Let me explain this crafting process in detail...",
        @"This technique requires some practice, but once you master it, you'll create amazing pieces!",
        [NSString stringWithFormat:@"For this project, I recommend focusing on %@ techniques.", self.specialties.lastObject]
    ];
    
    NSUInteger randomIndex = arc4random_uniform((uint32_t)responses.count);
    return responses[randomIndex];
}

@end
