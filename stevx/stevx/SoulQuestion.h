//
//  SoulQuestion.h
//  SteVx - AI Steampunk Jewelry Makers
//
//  Created by <PERSON><PERSON><PERSON> on 2024/08/01.
//  Copyright © 2024 SteVx. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * Soul Question Model
 * Represents a daily AI soul question for user engagement
 */
@interface SoulQuestion : NSObject

@property (nonatomic, strong, readonly) NSString *questionId;
@property (nonatomic, strong, readonly) NSString *question;
@property (nonatomic, assign, readonly) NSInteger reward;

/**
 * Initialize Soul Question
 */
- (instancetype)initWithId:(NSString *)questionId
                  question:(NSString *)question
                    reward:(NSInteger)reward;

/**
 * Get all available soul questions
 */
+ (NSArray<SoulQuestion *> *)getAllQuestions;

/**
 * Get today's random question
 */
+ (SoulQuestion *)getTodaysQuestion;

/**
 * Check if user has answered today's question
 */
+ (BOOL)hasAnsweredTodaysQuestion;

/**
 * Mark today's question as answered
 */
+ (void)markTodaysQuestionAsAnswered;

/**
 * Get user's current coin balance
 */
+ (NSInteger)getCurrentCoinBalance;

/**
 * Add coins to user's balance
 */
+ (void)addCoins:(NSInteger)coins;

/**
 * Check if it's first time using the app
 */
+ (BOOL)isFirstTimeUser;

/**
 * Mark user as not first time
 */
+ (void)markUserAsNotFirstTime;

@end

NS_ASSUME_NONNULL_END
