//
//  SoulQuestionView.h
//  SteVx - AI Steampunk Jewelry Makers
//
//  Created by <PERSON><PERSON><PERSON> on 2024/08/01.
//  Copyright © 2024 SteVx. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@protocol SoulQuestionViewDelegate <NSObject>

- (void)soulQuestionViewDidCompleteQuestion:(NSInteger)reward;

@end

/**
 * Soul Question View
 * Interactive view for daily AI soul questions
 */
@interface SoulQuestionView : UIView

@property (nonatomic, weak) id<SoulQuestionViewDelegate> delegate;

/**
 * Initialize and setup the soul question view
 */
- (void)setupSoulQuestionView;

/**
 * Refresh the view state
 */
- (void)refreshView;

@end

NS_ASSUME_NONNULL_END
