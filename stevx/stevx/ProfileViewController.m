//
//  ProfileViewController.m
//  SteVx - AI Steampunk Jewelry Makers
//
//  Created by Developer on 2024/08/01.
//  Copyright © 2024 SteVx. All rights reserved.
//

#import "ProfileViewController.h"
#import "ThemeManager.h"
#import "CartoonAvatarView.h"
#import "SoulQuestion.h"
#import "SoulQuestionView.h"

@interface ProfileViewController () <UICollectionViewDataSource, UICollectionViewDelegate, UICollectionViewDelegateFlowLayout, SoulQuestionViewDelegate>

@property (nonatomic, strong) UIScrollView *scrollView;
@property (nonatomic, strong) UIView *contentView;
@property (nonatomic, strong) CartoonAvatarView *profileAvatarView;
@property (nonatomic, strong) UILabel *nameLabel;
@property (nonatomic, strong) UILabel *usernameLabel;
@property (nonatomic, strong) UILabel *bioLabel;
@property (nonatomic, strong) UIView *statsContainer;
@property (nonatomic, strong) UIButton *followButton;
@property (nonatomic, strong) UIButton *moreButton;
@property (nonatomic, strong) SoulQuestionView *soulQuestionView;
@property (nonatomic, strong) UICollectionView *portfolioCollectionView;

@end

@implementation ProfileViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self setupUI];
    [self setupConstraints];
    [self loadUserData];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    // Update coin balance
    [self updateStatsDisplay];
}

#pragma mark - Setup Methods

- (void)setupUI {
    ThemeManager *theme = [ThemeManager sharedManager];
    
    self.view.backgroundColor = theme.background;
    
    // Setup scroll view
    self.scrollView = [[UIScrollView alloc] init];
    self.scrollView.translatesAutoresizingMaskIntoConstraints = NO;
    self.scrollView.showsVerticalScrollIndicator = NO;
    [self.view addSubview:self.scrollView];
    
    // Content view
    self.contentView = [[UIView alloc] init];
    self.contentView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.scrollView addSubview:self.contentView];
    
    [self setupProfileHeader];
    [self setupStatsSection];
    [self setupActionButtons];
    [self setupSoulQuestionView];
    [self setupPortfolioSection];
}

- (void)setupProfileHeader {
    ThemeManager *theme = [ThemeManager sharedManager];
    
    // Profile header container with gradient
    UIView *headerContainer = [[UIView alloc] init];
    headerContainer.translatesAutoresizingMaskIntoConstraints = NO;
    
    CAGradientLayer *gradientLayer = [theme createGradientLayerWithColors:theme.primaryGradient];
    gradientLayer.frame = CGRectMake(0, 0, self.view.bounds.size.width, 300);
    [headerContainer.layer addSublayer:gradientLayer];
    
    [self.contentView addSubview:headerContainer];
    
    // Profile avatar
    self.profileAvatarView = [[CartoonAvatarView alloc] initWithAvatarType:CartoonAvatarTypeUser];
    self.profileAvatarView.translatesAutoresizingMaskIntoConstraints = NO;
    [headerContainer addSubview:self.profileAvatarView];
    
    // Name label
    self.nameLabel = [[UILabel alloc] init];
    self.nameLabel.text = @"Steampunk Crafter";
    self.nameLabel.font = theme.titleFont;
    self.nameLabel.textColor = theme.textDark;
    self.nameLabel.textAlignment = NSTextAlignmentCenter;
    self.nameLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [headerContainer addSubview:self.nameLabel];
    
    // Username label
    self.usernameLabel = [[UILabel alloc] init];
    self.usernameLabel.text = @"@steampunkcrafter";
    self.usernameLabel.font = theme.bodyFont;
    self.usernameLabel.textColor = theme.brass;
    self.usernameLabel.textAlignment = NSTextAlignmentCenter;
    self.usernameLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [headerContainer addSubview:self.usernameLabel];
    
    // Bio label
    self.bioLabel = [[UILabel alloc] init];
    self.bioLabel.text = @"Join to building the future\nThe most trusted way to build future";
    self.bioLabel.font = theme.captionFont;
    self.bioLabel.textColor = theme.textDark;
    self.bioLabel.textAlignment = NSTextAlignmentCenter;
    self.bioLabel.numberOfLines = 0;
    self.bioLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [headerContainer addSubview:self.bioLabel];
    
    // Header constraints
    [NSLayoutConstraint activateConstraints:@[
        [headerContainer.topAnchor constraintEqualToAnchor:self.contentView.topAnchor],
        [headerContainer.leadingAnchor constraintEqualToAnchor:self.contentView.leadingAnchor],
        [headerContainer.trailingAnchor constraintEqualToAnchor:self.contentView.trailingAnchor],
        [headerContainer.heightAnchor constraintEqualToConstant:300],
        
        [self.profileAvatarView.centerXAnchor constraintEqualToAnchor:headerContainer.centerXAnchor],
        [self.profileAvatarView.topAnchor constraintEqualToAnchor:headerContainer.topAnchor constant:60],
        [self.profileAvatarView.widthAnchor constraintEqualToConstant:100],
        [self.profileAvatarView.heightAnchor constraintEqualToConstant:100],
        
        [self.nameLabel.topAnchor constraintEqualToAnchor:self.profileAvatarView.bottomAnchor constant:16],
        [self.nameLabel.leadingAnchor constraintEqualToAnchor:headerContainer.leadingAnchor constant:20],
        [self.nameLabel.trailingAnchor constraintEqualToAnchor:headerContainer.trailingAnchor constant:-20],
        
        [self.usernameLabel.topAnchor constraintEqualToAnchor:self.nameLabel.bottomAnchor constant:4],
        [self.usernameLabel.leadingAnchor constraintEqualToAnchor:headerContainer.leadingAnchor constant:20],
        [self.usernameLabel.trailingAnchor constraintEqualToAnchor:headerContainer.trailingAnchor constant:-20],
        
        [self.bioLabel.topAnchor constraintEqualToAnchor:self.usernameLabel.bottomAnchor constant:12],
        [self.bioLabel.leadingAnchor constraintEqualToAnchor:headerContainer.leadingAnchor constant:20],
        [self.bioLabel.trailingAnchor constraintEqualToAnchor:headerContainer.trailingAnchor constant:-20],
        [self.bioLabel.bottomAnchor constraintEqualToAnchor:headerContainer.bottomAnchor constant:-20]
    ]];
}

- (void)setupStatsSection {
    ThemeManager *theme = [ThemeManager sharedManager];
    
    self.statsContainer = [[UIView alloc] init];
    self.statsContainer.translatesAutoresizingMaskIntoConstraints = NO;
    [self.contentView addSubview:self.statsContainer];
    
    // Create stat cards
    UIView *followersCard = [self createStatCardWithValue:@"1,432" label:@"Followers"];
    UIView *coinValueCard = [self createStatCardWithValue:@"$275" label:@"Coin Value"];
    UIView *nftOwnedCard = [self createStatCardWithValue:@"279" label:@"NFT Owned"];
    
    [self.statsContainer addSubview:followersCard];
    [self.statsContainer addSubview:coinValueCard];
    [self.statsContainer addSubview:nftOwnedCard];
    
    // Stats constraints
    [NSLayoutConstraint activateConstraints:@[
        [followersCard.leadingAnchor constraintEqualToAnchor:self.statsContainer.leadingAnchor],
        [followersCard.topAnchor constraintEqualToAnchor:self.statsContainer.topAnchor],
        [followersCard.bottomAnchor constraintEqualToAnchor:self.statsContainer.bottomAnchor],
        
        [coinValueCard.leadingAnchor constraintEqualToAnchor:followersCard.trailingAnchor constant:12],
        [coinValueCard.topAnchor constraintEqualToAnchor:self.statsContainer.topAnchor],
        [coinValueCard.bottomAnchor constraintEqualToAnchor:self.statsContainer.bottomAnchor],
        [coinValueCard.widthAnchor constraintEqualToAnchor:followersCard.widthAnchor],
        
        [nftOwnedCard.leadingAnchor constraintEqualToAnchor:coinValueCard.trailingAnchor constant:12],
        [nftOwnedCard.trailingAnchor constraintEqualToAnchor:self.statsContainer.trailingAnchor],
        [nftOwnedCard.topAnchor constraintEqualToAnchor:self.statsContainer.topAnchor],
        [nftOwnedCard.bottomAnchor constraintEqualToAnchor:self.statsContainer.bottomAnchor],
        [nftOwnedCard.widthAnchor constraintEqualToAnchor:followersCard.widthAnchor]
    ]];
}

- (UIView *)createStatCardWithValue:(NSString *)value label:(NSString *)label {
    ThemeManager *theme = [ThemeManager sharedManager];
    
    UIView *card = [theme createCardViewWithBackgroundColor:theme.textWhite];
    card.translatesAutoresizingMaskIntoConstraints = NO;
    
    UILabel *valueLabel = [[UILabel alloc] init];
    valueLabel.text = value;
    valueLabel.font = [UIFont boldSystemFontOfSize:18];
    valueLabel.textColor = theme.textDark;
    valueLabel.textAlignment = NSTextAlignmentCenter;
    valueLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [card addSubview:valueLabel];
    
    UILabel *labelLabel = [[UILabel alloc] init];
    labelLabel.text = label;
    labelLabel.font = [UIFont systemFontOfSize:12];
    labelLabel.textColor = [theme.textDark colorWithAlphaComponent:0.7];
    labelLabel.textAlignment = NSTextAlignmentCenter;
    labelLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [card addSubview:labelLabel];
    
    [NSLayoutConstraint activateConstraints:@[
        [card.heightAnchor constraintEqualToConstant:80],
        
        [valueLabel.centerXAnchor constraintEqualToAnchor:card.centerXAnchor],
        [valueLabel.topAnchor constraintEqualToAnchor:card.topAnchor constant:16],
        
        [labelLabel.centerXAnchor constraintEqualToAnchor:card.centerXAnchor],
        [labelLabel.topAnchor constraintEqualToAnchor:valueLabel.bottomAnchor constant:4],
        [labelLabel.bottomAnchor constraintEqualToAnchor:card.bottomAnchor constant:-16]
    ]];
    
    return card;
}

- (void)setupActionButtons {
    ThemeManager *theme = [ThemeManager sharedManager];
    
    UIView *buttonContainer = [[UIView alloc] init];
    buttonContainer.translatesAutoresizingMaskIntoConstraints = NO;
    [self.contentView addSubview:buttonContainer];
    
    // Follow button
    self.followButton = [theme createBrassButtonWithTitle:@"Follow"];
    self.followButton.translatesAutoresizingMaskIntoConstraints = NO;
    [buttonContainer addSubview:self.followButton];
    
    // More button
    self.moreButton = [[UIButton alloc] init];
    self.moreButton.backgroundColor = theme.textWhite;
    self.moreButton.layer.cornerRadius = 24;
    self.moreButton.layer.borderWidth = 2;
    self.moreButton.layer.borderColor = theme.primaryDark.CGColor;
    [self.moreButton setTitle:@"⋯" forState:UIControlStateNormal];
    [self.moreButton setTitleColor:theme.textDark forState:UIControlStateNormal];
    self.moreButton.translatesAutoresizingMaskIntoConstraints = NO;
    [buttonContainer addSubview:self.moreButton];
    
    [NSLayoutConstraint activateConstraints:@[
        [buttonContainer.heightAnchor constraintEqualToConstant:48],
        
        [self.followButton.leadingAnchor constraintEqualToAnchor:buttonContainer.leadingAnchor],
        [self.followButton.topAnchor constraintEqualToAnchor:buttonContainer.topAnchor],
        [self.followButton.bottomAnchor constraintEqualToAnchor:buttonContainer.bottomAnchor],
        [self.followButton.trailingAnchor constraintEqualToAnchor:self.moreButton.leadingAnchor constant:-12],
        
        [self.moreButton.trailingAnchor constraintEqualToAnchor:buttonContainer.trailingAnchor],
        [self.moreButton.topAnchor constraintEqualToAnchor:buttonContainer.topAnchor],
        [self.moreButton.bottomAnchor constraintEqualToAnchor:buttonContainer.bottomAnchor],
        [self.moreButton.widthAnchor constraintEqualToConstant:48]
    ]];
}

- (void)setupSoulQuestionView {
    self.soulQuestionView = [[SoulQuestionView alloc] init];
    self.soulQuestionView.translatesAutoresizingMaskIntoConstraints = NO;
    self.soulQuestionView.delegate = self;
    [self.contentView addSubview:self.soulQuestionView];

    [NSLayoutConstraint activateConstraints:@[
        [self.soulQuestionView.heightAnchor constraintEqualToConstant:200]
    ]];
}

- (void)setupPortfolioSection {
    // Portfolio collection view setup
    UICollectionViewFlowLayout *layout = [[UICollectionViewFlowLayout alloc] init];
    layout.scrollDirection = UICollectionViewScrollDirectionVertical;
    layout.minimumInteritemSpacing = 12;
    layout.minimumLineSpacing = 12;
    
    self.portfolioCollectionView = [[UICollectionView alloc] initWithFrame:CGRectZero collectionViewLayout:layout];
    self.portfolioCollectionView.translatesAutoresizingMaskIntoConstraints = NO;
    self.portfolioCollectionView.backgroundColor = [UIColor clearColor];
    self.portfolioCollectionView.dataSource = self;
    self.portfolioCollectionView.delegate = self;
    self.portfolioCollectionView.scrollEnabled = NO;
    
    [self.portfolioCollectionView registerClass:[PortfolioCollectionViewCell class] 
                          forCellWithReuseIdentifier:@"PortfolioCell"];
    
    [self.contentView addSubview:self.portfolioCollectionView];
}

- (void)setupConstraints {
    [NSLayoutConstraint activateConstraints:@[
        // Scroll view
        [self.scrollView.topAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.topAnchor],
        [self.scrollView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [self.scrollView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [self.scrollView.bottomAnchor constraintEqualToAnchor:self.view.bottomAnchor],
        
        // Content view
        [self.contentView.topAnchor constraintEqualToAnchor:self.scrollView.topAnchor],
        [self.contentView.leadingAnchor constraintEqualToAnchor:self.scrollView.leadingAnchor],
        [self.contentView.trailingAnchor constraintEqualToAnchor:self.scrollView.trailingAnchor],
        [self.contentView.bottomAnchor constraintEqualToAnchor:self.scrollView.bottomAnchor],
        [self.contentView.widthAnchor constraintEqualToAnchor:self.scrollView.widthAnchor],
        
        // Stats container
        [self.statsContainer.topAnchor constraintEqualToAnchor:self.contentView.topAnchor constant:320],
        [self.statsContainer.leadingAnchor constraintEqualToAnchor:self.contentView.leadingAnchor constant:20],
        [self.statsContainer.trailingAnchor constraintEqualToAnchor:self.contentView.trailingAnchor constant:-20],
        
        // Action buttons (positioned after stats)
        // Soul question view
        [self.soulQuestionView.topAnchor constraintEqualToAnchor:self.statsContainer.bottomAnchor constant:80],
        [self.soulQuestionView.leadingAnchor constraintEqualToAnchor:self.contentView.leadingAnchor constant:20],
        [self.soulQuestionView.trailingAnchor constraintEqualToAnchor:self.contentView.trailingAnchor constant:-20],
        
        // Portfolio collection view
        [self.portfolioCollectionView.topAnchor constraintEqualToAnchor:self.soulQuestionView.bottomAnchor constant:20],
        [self.portfolioCollectionView.leadingAnchor constraintEqualToAnchor:self.contentView.leadingAnchor constant:20],
        [self.portfolioCollectionView.trailingAnchor constraintEqualToAnchor:self.contentView.trailingAnchor constant:-20],
        [self.portfolioCollectionView.heightAnchor constraintEqualToConstant:400],
        [self.portfolioCollectionView.bottomAnchor constraintEqualToAnchor:self.contentView.bottomAnchor constant:-20]
    ]];
}

#pragma mark - Data Methods

- (void)loadUserData {
    // Load user data and update UI
    [self updateStatsDisplay];
}

- (void)updateStatsDisplay {
    NSInteger coinBalance = [SoulQuestion getCurrentCoinBalance];
    // Update coin value display
    // This would be implemented to update the stats cards
}

#pragma mark - SoulQuestionViewDelegate

- (void)soulQuestionViewDidCompleteQuestion:(NSInteger)reward {
    [self updateStatsDisplay];

    // Show celebration animation or update UI
    NSLog(@"User completed soul question and earned %ld coins!", (long)reward);
}

#pragma mark - UICollectionViewDataSource

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return 4; // Portfolio items
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView 
                  cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    PortfolioCollectionViewCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"PortfolioCell" 
                                                                                  forIndexPath:indexPath];
    
    NSArray *portfolioData = @[
        @{@"title": @"Gear Ring", @"time": @"12h : 35m : 45s", @"tag": @"Yesterday"},
        @{@"title": @"Steam Pendant", @"time": @"8h : 19m : 20s", @"tag": @"#steampunk"},
        @{@"title": @"Brass Bracelet", @"time": @"17h : 47m : 16s", @"tag": @"Yesterday"},
        @{@"title": @"Vintage Brooch", @"time": @"20h : 21m : 10s", @"tag": @"#vintage"}
    ];
    
    [cell configureWithData:portfolioData[indexPath.item] atIndex:indexPath.item];
    
    return cell;
}

#pragma mark - UICollectionViewDelegateFlowLayout

- (CGSize)collectionView:(UICollectionView *)collectionView 
                  layout:(UICollectionViewLayout *)collectionViewLayout 
  sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    CGFloat width = (collectionView.bounds.size.width - 12) / 2;
    CGFloat height = width * 1.25;
    return CGSizeMake(width, height);
}

@end

#pragma mark - PortfolioCollectionViewCell

@interface PortfolioCollectionViewCell : UICollectionViewCell

- (void)configureWithData:(NSDictionary *)data atIndex:(NSInteger)index;

@end

@implementation PortfolioCollectionViewCell

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    // Portfolio cell UI setup will be implemented
    ThemeManager *theme = [ThemeManager sharedManager];
    self.backgroundColor = [theme.brass colorWithAlphaComponent:0.1];
    self.layer.cornerRadius = 16;
    self.layer.borderWidth = 2;
    self.layer.borderColor = theme.brass.CGColor;
}

- (void)configureWithData:(NSDictionary *)data atIndex:(NSInteger)index {
    // Configure cell with portfolio data
}

@end
