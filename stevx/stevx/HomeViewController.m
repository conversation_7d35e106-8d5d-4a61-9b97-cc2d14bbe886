//
//  HomeViewController.m
//  SteVx - AI Steampunk Jewelry Makers
//
//  Created by Dev<PERSON>per on 2024/08/01.
//  Copyright © 2024 SteVx. All rights reserved.
//

#import "HomeViewController.h"
#import "ThemeManager.h"
#import "AICharacter.h"
#import "CartoonAvatarView.h"
#import "CharacterDetailViewController.h"

@interface HomeViewController () <UICollectionViewDataSource, UICollectionViewDelegate, UICollectionViewDelegateFlowLayout>

@property (nonatomic, strong) UICollectionView *collectionView;
@property (nonatomic, strong) NSArray<AICharacter *> *characters;
@property (nonatomic, strong) UIView *headerView;
@property (nonatomic, strong) CartoonAvatarView *headerAvatarView;

@end

@implementation HomeViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self setupData];
    [self setupUI];
    [self setupConstraints];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    // Style navigation bar
    [[ThemeManager sharedManager] styleNavigationBar:self.navigationController.navigationBar];
}

#pragma mark - Setup Methods

- (void)setupData {
    self.characters = [AICharacter getAllCharacters];
}

- (void)setupUI {
    ThemeManager *theme = [ThemeManager sharedManager];
    
    self.view.backgroundColor = theme.background;
    self.title = @"Stevx";
    
    // Setup header view
    [self setupHeaderView];
    
    // Setup collection view
    [self setupCollectionView];
}

- (void)setupHeaderView {
    ThemeManager *theme = [ThemeManager sharedManager];
    
    self.headerView = [[UIView alloc] init];
    self.headerView.translatesAutoresizingMaskIntoConstraints = NO;
    
    // Gradient background
    CAGradientLayer *gradientLayer = [theme createGradientLayerWithColors:theme.primaryGradient];
    gradientLayer.frame = CGRectMake(0, 0, self.view.bounds.size.width, 200);
    [self.headerView.layer addSublayer:gradientLayer];
    
    // Header avatar
    self.headerAvatarView = [[CartoonAvatarView alloc] initWithAvatarType:CartoonAvatarTypeGearMaster];
    self.headerAvatarView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.headerView addSubview:self.headerAvatarView];
    
    // Welcome card
    UIView *welcomeCard = [theme createCardViewWithBackgroundColor:theme.textWhite];
    welcomeCard.translatesAutoresizingMaskIntoConstraints = NO;
    
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.text = @"Your AI Steampunk Jewelry Makers";
    titleLabel.font = theme.subtitleFont;
    titleLabel.textColor = theme.textDark;
    titleLabel.numberOfLines = 0;
    titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [welcomeCard addSubview:titleLabel];
    
    UILabel *subtitleLabel = [[UILabel alloc] init];
    subtitleLabel.text = @"Choose an AI expert to start your steampunk jewelry crafting journey";
    subtitleLabel.font = theme.captionFont;
    subtitleLabel.textColor = [theme.textDark colorWithAlphaComponent:0.7];
    subtitleLabel.numberOfLines = 0;
    subtitleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [welcomeCard addSubview:subtitleLabel];
    
    [self.headerView addSubview:welcomeCard];
    [self.view addSubview:self.headerView];
    
    // Setup constraints for header elements
    [NSLayoutConstraint activateConstraints:@[
        // Header avatar
        [self.headerAvatarView.centerXAnchor constraintEqualToAnchor:self.headerView.centerXAnchor],
        [self.headerAvatarView.topAnchor constraintEqualToAnchor:self.headerView.topAnchor constant:20],
        [self.headerAvatarView.widthAnchor constraintEqualToConstant:80],
        [self.headerAvatarView.heightAnchor constraintEqualToConstant:80],
        
        // Welcome card
        [welcomeCard.leadingAnchor constraintEqualToAnchor:self.headerView.leadingAnchor constant:20],
        [welcomeCard.trailingAnchor constraintEqualToAnchor:self.headerView.trailingAnchor constant:-20],
        [welcomeCard.topAnchor constraintEqualToAnchor:self.headerAvatarView.bottomAnchor constant:20],
        [welcomeCard.bottomAnchor constraintEqualToAnchor:self.headerView.bottomAnchor constant:-20],
        
        // Title label
        [titleLabel.topAnchor constraintEqualToAnchor:welcomeCard.topAnchor constant:24],
        [titleLabel.leadingAnchor constraintEqualToAnchor:welcomeCard.leadingAnchor constant:24],
        [titleLabel.trailingAnchor constraintEqualToAnchor:welcomeCard.trailingAnchor constant:-24],
        
        // Subtitle label
        [subtitleLabel.topAnchor constraintEqualToAnchor:titleLabel.bottomAnchor constant:8],
        [subtitleLabel.leadingAnchor constraintEqualToAnchor:welcomeCard.leadingAnchor constant:24],
        [subtitleLabel.trailingAnchor constraintEqualToAnchor:welcomeCard.trailingAnchor constant:-24],
        [subtitleLabel.bottomAnchor constraintEqualToAnchor:welcomeCard.bottomAnchor constant:-24]
    ]];
}

- (void)setupCollectionView {
    UICollectionViewFlowLayout *layout = [[UICollectionViewFlowLayout alloc] init];
    layout.scrollDirection = UICollectionViewScrollDirectionVertical;
    layout.minimumInteritemSpacing = 16;
    layout.minimumLineSpacing = 16;
    layout.sectionInset = UIEdgeInsetsMake(20, 20, 100, 20);
    
    self.collectionView = [[UICollectionView alloc] initWithFrame:CGRectZero collectionViewLayout:layout];
    self.collectionView.translatesAutoresizingMaskIntoConstraints = NO;
    self.collectionView.backgroundColor = [UIColor clearColor];
    self.collectionView.dataSource = self;
    self.collectionView.delegate = self;
    
    [self.collectionView registerClass:[CharacterCollectionViewCell class] 
            forCellWithReuseIdentifier:@"CharacterCell"];
    
    [self.view addSubview:self.collectionView];
}

- (void)setupConstraints {
    [NSLayoutConstraint activateConstraints:@[
        // Header view
        [self.headerView.topAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.topAnchor],
        [self.headerView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [self.headerView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [self.headerView.heightAnchor constraintEqualToConstant:200],
        
        // Collection view
        [self.collectionView.topAnchor constraintEqualToAnchor:self.headerView.bottomAnchor],
        [self.collectionView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [self.collectionView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [self.collectionView.bottomAnchor constraintEqualToAnchor:self.view.bottomAnchor]
    ]];
}

#pragma mark - UICollectionViewDataSource

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.characters.count;
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView 
                  cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    CharacterCollectionViewCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"CharacterCell" 
                                                                                  forIndexPath:indexPath];
    
    AICharacter *character = self.characters[indexPath.item];
    [cell configureWithCharacter:character atIndex:indexPath.item];
    
    return cell;
}

#pragma mark - UICollectionViewDelegate

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    AICharacter *character = self.characters[indexPath.item];
    
    CharacterDetailViewController *detailVC = [[CharacterDetailViewController alloc] initWithCharacter:character];
    [self.navigationController pushViewController:detailVC animated:YES];
}

#pragma mark - UICollectionViewDelegateFlowLayout

- (CGSize)collectionView:(UICollectionView *)collectionView 
                  layout:(UICollectionViewLayout *)collectionViewLayout 
  sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    CGFloat width = (collectionView.bounds.size.width - 56) / 2; // 20 + 16 + 20 margins
    CGFloat height = width * 1.25; // 4:5 aspect ratio
    return CGSizeMake(width, height);
}

@end

#pragma mark - CharacterCollectionViewCell

@interface CharacterCollectionViewCell : UICollectionViewCell

@property (nonatomic, strong) CartoonAvatarView *avatarView;
@property (nonatomic, strong) UILabel *nameLabel;
@property (nonatomic, strong) UILabel *descriptionLabel;
@property (nonatomic, strong) UIView *cardView;

- (void)configureWithCharacter:(AICharacter *)character atIndex:(NSInteger)index;

@end

@implementation CharacterCollectionViewCell

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    ThemeManager *theme = [ThemeManager sharedManager];
    
    // Card view
    self.cardView = [theme createCardViewWithBackgroundColor:[theme.brass colorWithAlphaComponent:0.1]];
    self.cardView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.contentView addSubview:self.cardView];
    
    // Avatar container
    UIView *avatarContainer = [[UIView alloc] init];
    avatarContainer.translatesAutoresizingMaskIntoConstraints = NO;
    avatarContainer.backgroundColor = theme.brass;
    avatarContainer.layer.cornerRadius = 18;
    avatarContainer.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
    [self.cardView addSubview:avatarContainer];
    
    // Avatar view
    self.avatarView = [[CartoonAvatarView alloc] initWithAvatarType:CartoonAvatarTypeUser];
    self.avatarView.translatesAutoresizingMaskIntoConstraints = NO;
    self.avatarView.showBorder = NO;
    [avatarContainer addSubview:self.avatarView];
    
    // Name label
    self.nameLabel = [[UILabel alloc] init];
    self.nameLabel.font = [UIFont boldSystemFontOfSize:14];
    self.nameLabel.textColor = theme.textDark;
    self.nameLabel.textAlignment = NSTextAlignmentCenter;
    self.nameLabel.numberOfLines = 1;
    self.nameLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.cardView addSubview:self.nameLabel];
    
    // Description label
    self.descriptionLabel = [[UILabel alloc] init];
    self.descriptionLabel.font = [UIFont systemFontOfSize:11];
    self.descriptionLabel.textColor = [theme.textDark colorWithAlphaComponent:0.7];
    self.descriptionLabel.textAlignment = NSTextAlignmentCenter;
    self.descriptionLabel.numberOfLines = 2;
    self.descriptionLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.cardView addSubview:self.descriptionLabel];
    
    // Setup constraints
    [NSLayoutConstraint activateConstraints:@[
        // Card view
        [self.cardView.topAnchor constraintEqualToAnchor:self.contentView.topAnchor],
        [self.cardView.leadingAnchor constraintEqualToAnchor:self.contentView.leadingAnchor],
        [self.cardView.trailingAnchor constraintEqualToAnchor:self.contentView.trailingAnchor],
        [self.cardView.bottomAnchor constraintEqualToAnchor:self.contentView.bottomAnchor],
        
        // Avatar container
        [avatarContainer.topAnchor constraintEqualToAnchor:self.cardView.topAnchor],
        [avatarContainer.leadingAnchor constraintEqualToAnchor:self.cardView.leadingAnchor],
        [avatarContainer.trailingAnchor constraintEqualToAnchor:self.cardView.trailingAnchor],
        [avatarContainer.heightAnchor constraintEqualToAnchor:self.cardView.heightAnchor multiplier:0.6],
        
        // Avatar view
        [self.avatarView.centerXAnchor constraintEqualToAnchor:avatarContainer.centerXAnchor],
        [self.avatarView.centerYAnchor constraintEqualToAnchor:avatarContainer.centerYAnchor],
        [self.avatarView.widthAnchor constraintEqualToConstant:60],
        [self.avatarView.heightAnchor constraintEqualToConstant:60],
        
        // Name label
        [self.nameLabel.topAnchor constraintEqualToAnchor:avatarContainer.bottomAnchor constant:12],
        [self.nameLabel.leadingAnchor constraintEqualToAnchor:self.cardView.leadingAnchor constant:12],
        [self.nameLabel.trailingAnchor constraintEqualToAnchor:self.cardView.trailingAnchor constant:-12],
        
        // Description label
        [self.descriptionLabel.topAnchor constraintEqualToAnchor:self.nameLabel.bottomAnchor constant:4],
        [self.descriptionLabel.leadingAnchor constraintEqualToAnchor:self.cardView.leadingAnchor constant:12],
        [self.descriptionLabel.trailingAnchor constraintEqualToAnchor:self.cardView.trailingAnchor constant:-12],
        [self.descriptionLabel.bottomAnchor constraintLessThanOrEqualToAnchor:self.cardView.bottomAnchor constant:-12]
    ]];
}

- (void)configureWithCharacter:(AICharacter *)character atIndex:(NSInteger)index {
    self.nameLabel.text = character.name;
    self.descriptionLabel.text = character.shortDescription;
    
    // Set avatar type based on index
    CartoonAvatarType avatarTypes[] = {
        CartoonAvatarTypeGearMaster, CartoonAvatarTypeSteampunkLady, CartoonAvatarTypeInventor, 
        CartoonAvatarTypeUser, CartoonAvatarTypeCrafter, CartoonAvatarTypeGearMaster,
        CartoonAvatarTypeSteampunkLady, CartoonAvatarTypeInventor, CartoonAvatarTypeUser, CartoonAvatarTypeCrafter
    };
    
    self.avatarView.avatarType = avatarTypes[index % 5];
    
    // Set card border color based on index
    ThemeManager *theme = [ThemeManager sharedManager];
    UIColor *colors[] = {
        theme.brass, theme.accent, theme.primaryDark, theme.brass, theme.accent,
        theme.primaryDark, theme.brass, theme.accent, theme.primaryDark, theme.brass
    };
    
    self.cardView.layer.borderColor = colors[index % 5].CGColor;
}

@end
