//
//  CartoonAvatarView.m
//  SteVx - AI Steampunk Jewelry Makers
//
//  Created by Developer on 2024/08/01.
//  Copyright © 2024 SteVx. All rights reserved.
//

#import "CartoonAvatarView.h"
#import "ThemeManager.h"

@implementation CartoonAvatarView

- (instancetype)initWithAvatarType:(CartoonAvatarType)avatarType {
    self = [super init];
    if (self) {
        [self commonInit];
        _avatarType = avatarType;
    }
    return self;
}

- (instancetype)initWithFrame:(CGRect)frame avatarType:(CartoonAvatarType)avatarType {
    self = [super initWithFrame:frame];
    if (self) {
        [self commonInit];
        _avatarType = avatarType;
    }
    return self;
}

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self commonInit];
    }
    return self;
}

- (instancetype)initWithCoder:(NSCoder *)coder {
    self = [super initWithCoder:coder];
    if (self) {
        [self commonInit];
    }
    return self;
}

- (void)commonInit {
    _avatarType = CartoonAvatarTypeUser;
    _showBorder = YES;
    _borderColor = [ThemeManager sharedManager].brass;
    _borderWidth = 3.0;
    
    self.backgroundColor = [UIColor clearColor];
    self.layer.masksToBounds = YES;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    self.layer.cornerRadius = MIN(self.bounds.size.width, self.bounds.size.height) / 2.0;
}

- (void)setAvatarType:(CartoonAvatarType)avatarType animated:(BOOL)animated {
    _avatarType = avatarType;
    
    if (animated) {
        [UIView transitionWithView:self
                          duration:0.3
                           options:UIViewAnimationOptionTransitionCrossDissolve
                        animations:^{
                            [self setNeedsDisplay];
                        }
                        completion:nil];
    } else {
        [self setNeedsDisplay];
    }
}

- (void)drawRect:(CGRect)rect {
    CGContextRef context = UIGraphicsGetCurrentContext();
    CGPoint center = CGPointMake(rect.size.width / 2.0, rect.size.height / 2.0);
    CGFloat radius = MIN(rect.size.width, rect.size.height) / 2.0;
    
    // Draw border if enabled
    if (self.showBorder) {
        CGContextSetStrokeColorWithColor(context, self.borderColor.CGColor);
        CGContextSetLineWidth(context, self.borderWidth);
        CGContextStrokeEllipseInRect(context, CGRectInset(rect, self.borderWidth/2, self.borderWidth/2));
    }
    
    // Clip to circle
    CGContextAddEllipseInRect(context, CGRectInset(rect, self.borderWidth, self.borderWidth));
    CGContextClip(context);
    
    // Draw avatar based on type
    switch (self.avatarType) {
        case CartoonAvatarTypeUser:
            [self drawUserAvatar:context center:center radius:radius - self.borderWidth];
            break;
        case CartoonAvatarTypeGearMaster:
            [self drawGearMasterAvatar:context center:center radius:radius - self.borderWidth];
            break;
        case CartoonAvatarTypeSteampunkLady:
            [self drawSteampunkLadyAvatar:context center:center radius:radius - self.borderWidth];
            break;
        case CartoonAvatarTypeInventor:
            [self drawInventorAvatar:context center:center radius:radius - self.borderWidth];
            break;
        case CartoonAvatarTypeCrafter:
            [self drawCrafterAvatar:context center:center radius:radius - self.borderWidth];
            break;
    }
}

#pragma mark - Drawing Methods

- (void)drawUserAvatar:(CGContextRef)context center:(CGPoint)center radius:(CGFloat)radius {
    ThemeManager *theme = [ThemeManager sharedManager];
    
    // Background gradient
    [self drawRadialGradient:context center:center radius:radius 
                  startColor:[theme.brass colorWithAlphaComponent:0.8] 
                    endColor:theme.primaryDark];
    
    // Face
    CGContextSetFillColorWithColor(context, [UIColor colorWithRed:1.0 green:0.86 blue:0.71 alpha:1.0].CGColor);
    CGContextFillEllipseInRect(context, CGRectMake(center.x - radius * 0.6, center.y - radius * 0.5, radius * 1.2, radius * 1.2));
    
    // Steampunk goggles
    [self drawGoggles:context center:center radius:radius];
    
    // Hat
    [self drawSteampunkHat:context center:center radius:radius];
    
    // Mustache
    [self drawMustache:context center:center radius:radius];
}

- (void)drawGearMasterAvatar:(CGContextRef)context center:(CGPoint)center radius:(CGFloat)radius {
    ThemeManager *theme = [ThemeManager sharedManager];
    
    // Background gradient
    [self drawLinearGradient:context rect:CGRectMake(center.x - radius, center.y - radius, radius * 2, radius * 2)
                  startColor:theme.brass endColor:theme.primaryDark];
    
    // Gears decoration
    [self drawGears:context center:center radius:radius];
    
    // Face
    CGContextSetFillColorWithColor(context, [UIColor colorWithRed:1.0 green:0.86 blue:0.71 alpha:1.0].CGColor);
    CGContextFillEllipseInRect(context, CGRectMake(center.x - radius * 0.5, center.y - radius * 0.3, radius * 1.0, radius * 1.0));
    
    // Tools
    [self drawTools:context center:center radius:radius];
}

- (void)drawSteampunkLadyAvatar:(CGContextRef)context center:(CGPoint)center radius:(CGFloat)radius {
    ThemeManager *theme = [ThemeManager sharedManager];
    
    // Background gradient
    [self drawRadialGradient:context center:center radius:radius 
                  startColor:[theme.accent colorWithAlphaComponent:0.7] 
                    endColor:theme.primaryDark];
    
    // Face
    CGContextSetFillColorWithColor(context, [UIColor colorWithRed:1.0 green:0.86 blue:0.71 alpha:1.0].CGColor);
    CGContextFillEllipseInRect(context, CGRectMake(center.x - radius * 0.55, center.y - radius * 0.4, radius * 1.1, radius * 1.1));
    
    // Hair
    [self drawHair:context center:center radius:radius];
    
    // Jewelry
    [self drawJewelry:context center:center radius:radius];
}

- (void)drawInventorAvatar:(CGContextRef)context center:(CGPoint)center radius:(CGFloat)radius {
    ThemeManager *theme = [ThemeManager sharedManager];
    
    // Sweep gradient background
    [self drawSweepGradient:context center:center radius:radius colors:@[theme.brass, theme.primaryDark, theme.accent, theme.brass]];
    
    // Face
    CGContextSetFillColorWithColor(context, [UIColor colorWithRed:1.0 green:0.86 blue:0.71 alpha:1.0].CGColor);
    CGContextFillEllipseInRect(context, CGRectMake(center.x - radius * 0.5, center.y - radius * 0.35, radius * 1.0, radius * 1.0));
    
    // Invention gadgets
    [self drawInventionGadgets:context center:center radius:radius];
}

- (void)drawCrafterAvatar:(CGContextRef)context center:(CGPoint)center radius:(CGFloat)radius {
    // Similar to user avatar but with crafting tools
    [self drawUserAvatar:context center:center radius:radius];
    
    // Add crafting tools overlay
    [self drawCraftingTools:context center:center radius:radius];
}

#pragma mark - Helper Drawing Methods

- (void)drawRadialGradient:(CGContextRef)context center:(CGPoint)center radius:(CGFloat)radius
                startColor:(UIColor *)startColor endColor:(UIColor *)endColor {
    CGColorSpaceRef colorSpace = CGColorSpaceCreateDeviceRGB();
    NSArray *colors = @[(__bridge id)startColor.CGColor, (__bridge id)endColor.CGColor];
    CGFloat locations[] = {0.0, 1.0};

    CGGradientRef gradient = CGGradientCreateWithColors(colorSpace, (__bridge CFArrayRef)colors, locations);
    CGContextDrawRadialGradient(context, gradient, center, 0, center, radius, 0);

    CGGradientRelease(gradient);
    CGColorSpaceRelease(colorSpace);
}

- (void)drawLinearGradient:(CGContextRef)context rect:(CGRect)rect
                startColor:(UIColor *)startColor endColor:(UIColor *)endColor {
    CGColorSpaceRef colorSpace = CGColorSpaceCreateDeviceRGB();
    NSArray *colors = @[(__bridge id)startColor.CGColor, (__bridge id)endColor.CGColor];
    CGFloat locations[] = {0.0, 1.0};

    CGGradientRef gradient = CGGradientCreateWithColors(colorSpace, (__bridge CFArrayRef)colors, locations);
    CGContextDrawLinearGradient(context, gradient, rect.origin,
                               CGPointMake(rect.origin.x + rect.size.width, rect.origin.y + rect.size.height), 0);

    CGGradientRelease(gradient);
    CGColorSpaceRelease(colorSpace);
}

- (void)drawSweepGradient:(CGContextRef)context center:(CGPoint)center radius:(CGFloat)radius colors:(NSArray<UIColor *> *)colors {
    // Simplified sweep gradient using multiple radial gradients
    for (NSInteger i = 0; i < colors.count - 1; i++) {
        CGFloat startAngle = (2 * M_PI * i) / colors.count;
        CGFloat endAngle = (2 * M_PI * (i + 1)) / colors.count;

        CGContextSaveGState(context);

        // Create a wedge path
        CGMutablePathRef path = CGPathCreateMutable();
        CGPathMoveToPoint(path, NULL, center.x, center.y);
        CGPathAddArc(path, NULL, center.x, center.y, radius, startAngle, endAngle, false);
        CGPathCloseSubpath(path);

        CGContextAddPath(context, path);
        CGContextClip(context);

        [self drawRadialGradient:context center:center radius:radius
                      startColor:colors[i] endColor:colors[(i + 1) % colors.count]];

        CGPathRelease(path);
        CGContextRestoreGState(context);
    }
}

- (void)drawGoggles:(CGContextRef)context center:(CGPoint)center radius:(CGFloat)radius {
    ThemeManager *theme = [ThemeManager sharedManager];

    CGContextSetStrokeColorWithColor(context, theme.brass.CGColor);
    CGContextSetLineWidth(context, 3.0);

    // Left lens
    CGContextStrokeEllipseInRect(context, CGRectMake(center.x - radius * 0.45, center.y - radius * 0.3, radius * 0.4, radius * 0.4));

    // Right lens
    CGContextStrokeEllipseInRect(context, CGRectMake(center.x + radius * 0.05, center.y - radius * 0.3, radius * 0.4, radius * 0.4));

    // Bridge
    CGContextMoveToPoint(context, center.x - radius * 0.05, center.y - radius * 0.1);
    CGContextAddLineToPoint(context, center.x + radius * 0.05, center.y - radius * 0.1);
    CGContextStrokePath(context);
}

- (void)drawSteampunkHat:(CGContextRef)context center:(CGPoint)center radius:(CGFloat)radius {
    ThemeManager *theme = [ThemeManager sharedManager];

    // Hat main body
    CGContextSetFillColorWithColor(context, theme.primaryDark.CGColor);
    CGContextFillRect(context, CGRectMake(center.x - radius * 0.6, center.y - radius * 0.9, radius * 1.2, radius * 0.4));

    // Hat brim
    CGContextSetFillColorWithColor(context, theme.brass.CGColor);
    CGContextFillEllipseInRect(context, CGRectMake(center.x - radius * 0.7, center.y - radius * 0.6, radius * 1.4, radius * 0.2));
}

- (void)drawMustache:(CGContextRef)context center:(CGPoint)center radius:(CGFloat)radius {
    ThemeManager *theme = [ThemeManager sharedManager];

    CGContextSetFillColorWithColor(context, theme.primaryDark.CGColor);

    CGMutablePathRef path = CGPathCreateMutable();
    CGPathMoveToPoint(path, NULL, center.x - radius * 0.3, center.y + radius * 0.1);
    CGPathAddQuadCurveToPoint(path, NULL, center.x - radius * 0.1, center.y + radius * 0.2, center.x, center.y + radius * 0.1);
    CGPathAddQuadCurveToPoint(path, NULL, center.x + radius * 0.1, center.y + radius * 0.2, center.x + radius * 0.3, center.y + radius * 0.1);

    CGContextAddPath(context, path);
    CGContextFillPath(context);
    CGPathRelease(path);
}

- (void)drawGears:(CGContextRef)context center:(CGPoint)center radius:(CGFloat)radius {
    ThemeManager *theme = [ThemeManager sharedManager];

    CGContextSetStrokeColorWithColor(context, theme.brass.CGColor);
    CGContextSetLineWidth(context, 2.0);

    // Small gears
    [self drawGear:context center:CGPointMake(center.x - radius * 0.6, center.y - radius * 0.3) radius:radius * 0.15];
    [self drawGear:context center:CGPointMake(center.x + radius * 0.6, center.y + radius * 0.3) radius:radius * 0.12];
    [self drawGear:context center:CGPointMake(center.x + radius * 0.3, center.y - radius * 0.7) radius:radius * 0.1];
}

- (void)drawGear:(CGContextRef)context center:(CGPoint)center radius:(CGFloat)radius {
    const int teeth = 8;
    const CGFloat angleStep = 2 * M_PI / teeth;

    CGMutablePathRef path = CGPathCreateMutable();

    for (int i = 0; i < teeth; i++) {
        CGFloat angle = i * angleStep;
        CGFloat outerRadius = radius;
        CGFloat innerRadius = radius * 0.7;

        CGFloat x1 = center.x + cos(angle) * outerRadius;
        CGFloat y1 = center.y + sin(angle) * outerRadius;

        CGFloat x2 = center.x + cos(angle + angleStep * 0.3) * innerRadius;
        CGFloat y2 = center.y + sin(angle + angleStep * 0.3) * innerRadius;

        if (i == 0) {
            CGPathMoveToPoint(path, NULL, x1, y1);
        } else {
            CGPathAddLineToPoint(path, NULL, x1, y1);
        }
        CGPathAddLineToPoint(path, NULL, x2, y2);
    }
    CGPathCloseSubpath(path);

    CGContextAddPath(context, path);
    CGContextStrokePath(context);

    // Center circle
    CGContextStrokeEllipseInRect(context, CGRectMake(center.x - radius * 0.3, center.y - radius * 0.3, radius * 0.6, radius * 0.6));

    CGPathRelease(path);
}

- (void)drawTools:(CGContextRef)context center:(CGPoint)center radius:(CGFloat)radius {
    ThemeManager *theme = [ThemeManager sharedManager];

    CGContextSetStrokeColorWithColor(context, theme.accent.CGColor);
    CGContextSetLineWidth(context, 3.0);

    // Wrench
    CGContextMoveToPoint(context, center.x - radius * 0.8, center.y + radius * 0.5);
    CGContextAddLineToPoint(context, center.x - radius * 0.5, center.y + radius * 0.8);
    CGContextStrokePath(context);

    // Hammer
    CGContextMoveToPoint(context, center.x + radius * 0.5, center.y + radius * 0.6);
    CGContextAddLineToPoint(context, center.x + radius * 0.8, center.y + radius * 0.3);
    CGContextStrokePath(context);
}

- (void)drawHair:(CGContextRef)context center:(CGPoint)center radius:(CGFloat)radius {
    ThemeManager *theme = [ThemeManager sharedManager];

    CGContextSetFillColorWithColor(context, theme.accent.CGColor);

    CGMutablePathRef path = CGPathCreateMutable();
    CGPathMoveToPoint(path, NULL, center.x - radius * 0.6, center.y - radius * 0.3);
    CGPathAddQuadCurveToPoint(path, NULL, center.x - radius * 0.8, center.y - radius * 0.8, center.x, center.y - radius * 0.9);
    CGPathAddQuadCurveToPoint(path, NULL, center.x + radius * 0.8, center.y - radius * 0.8, center.x + radius * 0.6, center.y - radius * 0.3);

    CGContextAddPath(context, path);
    CGContextFillPath(context);
    CGPathRelease(path);
}

- (void)drawJewelry:(CGContextRef)context center:(CGPoint)center radius:(CGFloat)radius {
    ThemeManager *theme = [ThemeManager sharedManager];

    CGContextSetFillColorWithColor(context, theme.brass.CGColor);

    // Earrings
    CGContextFillEllipseInRect(context, CGRectMake(center.x - radius * 0.58, center.y - radius * 0.08, radius * 0.16, radius * 0.16));
    CGContextFillEllipseInRect(context, CGRectMake(center.x + radius * 0.42, center.y - radius * 0.08, radius * 0.16, radius * 0.16));

    // Necklace
    CGContextSetStrokeColorWithColor(context, theme.brass.CGColor);
    CGContextSetLineWidth(context, 2.0);
    CGContextAddArc(context, center.x, center.y + radius * 0.4, radius * 0.4, -M_PI * 0.8, M_PI * 1.6, false);
    CGContextStrokePath(context);
}

- (void)drawInventionGadgets:(CGContextRef)context center:(CGPoint)center radius:(CGFloat)radius {
    ThemeManager *theme = [ThemeManager sharedManager];

    CGContextSetStrokeColorWithColor(context, theme.brass.CGColor);
    CGContextSetLineWidth(context, 2.0);

    // Mechanical arm
    CGContextMoveToPoint(context, center.x - radius * 0.7, center.y);
    CGContextAddLineToPoint(context, center.x - radius * 0.4, center.y + radius * 0.3);
    CGContextStrokePath(context);

    // Steam pipe
    CGContextAddArc(context, center.x + radius * 0.6, center.y - radius * 0.2, radius * 0.15, 0, M_PI, false);
    CGContextStrokePath(context);

    // Invention glasses
    CGContextSetStrokeColorWithColor(context, theme.accent.CGColor);
    CGContextStrokeEllipseInRect(context, CGRectMake(center.x - radius * 0.35, center.y - radius * 0.25, radius * 0.3, radius * 0.3));
    CGContextStrokeEllipseInRect(context, CGRectMake(center.x + radius * 0.05, center.y - radius * 0.25, radius * 0.3, radius * 0.3));
}

- (void)drawCraftingTools:(CGContextRef)context center:(CGPoint)center radius:(CGFloat)radius {
    ThemeManager *theme = [ThemeManager sharedManager];

    CGContextSetStrokeColorWithColor(context, theme.warning.CGColor);
    CGContextSetLineWidth(context, 2.0);

    // Pliers
    CGContextMoveToPoint(context, center.x - radius * 0.9, center.y + radius * 0.7);
    CGContextAddLineToPoint(context, center.x - radius * 0.6, center.y + radius * 0.4);
    CGContextStrokePath(context);

    // Wire
    CGMutablePathRef path = CGPathCreateMutable();
    CGPathMoveToPoint(path, NULL, center.x + radius * 0.4, center.y + radius * 0.8);
    CGPathAddQuadCurveToPoint(path, NULL, center.x + radius * 0.6, center.y + radius * 0.6, center.x + radius * 0.8, center.y + radius * 0.8);
    CGContextAddPath(context, path);
    CGContextStrokePath(context);
    CGPathRelease(path);
}

@end
