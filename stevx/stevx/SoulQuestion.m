//
//  SoulQuestion.m
//  SteVx - AI Steampunk Jewelry Makers
//
//  Created by <PERSON><PERSON><PERSON> on 2024/08/01.
//  Copyright © 2024 SteVx. All rights reserved.
//

#import "SoulQuestion.h"

@interface SoulQuestion ()

@property (nonatomic, strong, readwrite) NSString *questionId;
@property (nonatomic, strong, readwrite) NSString *question;
@property (nonatomic, assign, readwrite) NSInteger reward;

@end

@implementation SoulQuestion

- (instancetype)initWithId:(NSString *)questionId
                  question:(NSString *)question
                    reward:(NSInteger)reward {
    self = [super init];
    if (self) {
        _questionId = questionId;
        _question = question;
        _reward = reward;
    }
    return self;
}

+ (NSArray<SoulQuestion *> *)getAllQuestions {
    static NSArray<SoulQuestion *> *questions = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        questions = @[
            [[SoulQuestion alloc] initWithId:@"q1"
                                    question:@"Which recent artwork impressed you the most?"
                                      reward:10],
            
            [[SoulQuestion alloc] initWithId:@"q2"
                                    question:@"What do you think is the most attractive aspect of steampunk style?"
                                      reward:10],
            
            [[SoulQuestion alloc] initWithId:@"q3"
                                    question:@"When making jewelry, which material texture do you prefer?"
                                      reward:10],
            
            [[SoulQuestion alloc] initWithId:@"q4"
                                    question:@"If you were to design an accessory that represents your personality, what would it look like?"
                                      reward:10],
            
            [[SoulQuestion alloc] initWithId:@"q5"
                                    question:@"What charm do you see in designs that combine vintage and modern elements?"
                                      reward:10],
            
            [[SoulQuestion alloc] initWithId:@"q6"
                                    question:@"Among all steampunk elements, which one do you love the most?"
                                      reward:10],
            
            [[SoulQuestion alloc] initWithId:@"q7"
                                    question:@"What differences do you see between handmade and machine-produced jewelry?"
                                      reward:10],
            
            [[SoulQuestion alloc] initWithId:@"q8"
                                    question:@"If you could travel to the steampunk era, what would you most want to experience?"
                                      reward:10],
            
            [[SoulQuestion alloc] initWithId:@"q9"
                                    question:@"What do you think gears and chains symbolize?"
                                      reward:10],
            
            [[SoulQuestion alloc] initWithId:@"q10"
                                    question:@"In the creative process, do you enjoy the design or the crafting phase more?"
                                      reward:10]
        ];
    });
    return questions;
}

+ (SoulQuestion *)getTodaysQuestion {
    NSArray<SoulQuestion *> *questions = [self getAllQuestions];
    
    // Get today's date string
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    formatter.dateFormat = @"yyyy-MM-dd";
    NSString *todayString = [formatter stringFromDate:[NSDate date]];
    
    // Check if we already have today's question stored
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    NSString *storedDate = [defaults stringForKey:@"lastQuestionDate"];
    NSString *storedQuestionId = [defaults stringForKey:@"todayQuestionId"];
    
    if ([todayString isEqualToString:storedDate] && storedQuestionId) {
        // Return stored question
        for (SoulQuestion *question in questions) {
            if ([question.questionId isEqualToString:storedQuestionId]) {
                return question;
            }
        }
    }
    
    // Generate new question for today
    NSInteger dayOfYear = [[NSCalendar currentCalendar] ordinalityOfUnit:NSCalendarUnitDay
                                                                  inUnit:NSCalendarUnitYear
                                                                 forDate:[NSDate date]];
    NSUInteger questionIndex = dayOfYear % questions.count;
    SoulQuestion *todaysQuestion = questions[questionIndex];
    
    // Store today's question
    [defaults setObject:todayString forKey:@"lastQuestionDate"];
    [defaults setObject:todaysQuestion.questionId forKey:@"todayQuestionId"];
    [defaults synchronize];
    
    return todaysQuestion;
}

+ (BOOL)hasAnsweredTodaysQuestion {
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    formatter.dateFormat = @"yyyy-MM-dd";
    NSString *todayString = [formatter stringFromDate:[NSDate date]];
    
    NSString *lastAnsweredDate = [defaults stringForKey:@"lastAnsweredDate"];
    return [todayString isEqualToString:lastAnsweredDate];
}

+ (void)markTodaysQuestionAsAnswered {
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    formatter.dateFormat = @"yyyy-MM-dd";
    NSString *todayString = [formatter stringFromDate:[NSDate date]];
    
    [defaults setObject:todayString forKey:@"lastAnsweredDate"];
    [defaults synchronize];
}

+ (NSInteger)getCurrentCoinBalance {
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    return [defaults integerForKey:@"coinBalance"];
}

+ (void)addCoins:(NSInteger)coins {
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    NSInteger currentBalance = [defaults integerForKey:@"coinBalance"];
    NSInteger newBalance = currentBalance + coins;
    [defaults setInteger:newBalance forKey:@"coinBalance"];
    [defaults synchronize];
}

+ (BOOL)isFirstTimeUser {
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    return ![defaults boolForKey:@"hasUsedAppBefore"];
}

+ (void)markUserAsNotFirstTime {
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    [defaults setBool:YES forKey:@"hasUsedAppBefore"];
    [defaults synchronize];
}

@end
