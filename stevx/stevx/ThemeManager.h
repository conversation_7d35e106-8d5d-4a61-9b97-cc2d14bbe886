//
//  ThemeManager.h
//  SteVx - AI Steampunk Jewelry Makers
//
//  Created by Developer on 2024/08/01.
//  Copyright © 2024 SteVx. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * Steampunk Theme Manager
 * Manages the color scheme and styling for the steampunk-themed app
 */
@interface ThemeManager : NSObject

+ (instancetype)sharedManager;

#pragma mark - Steampunk Color Palette

/// Primary dark color (深色背景) - #2C2C2C
@property (nonatomic, readonly) UIColor *primaryDark;

/// Brass color (黄铜色) - #C58B43
@property (nonatomic, readonly) UIColor *brass;

/// Accent color (酒红色) - #8B0000
@property (nonatomic, readonly) UIColor *accent;

/// Background color (旧纸张白) - #F7F2E8
@property (nonatomic, readonly) UIColor *background;

/// Text white color - #FFFFFF
@property (nonatomic, readonly) UIColor *textWhite;

/// Text dark color - #2C2C2C
@property (nonatomic, readonly) UIColor *textDark;

/// Overlay color (半透明遮罩) - #80000000
@property (nonatomic, readonly) UIColor *overlay;

/// Success color (成功色) - #4CAF50
@property (nonatomic, readonly) UIColor *success;

/// Warning color (警告色) - #FF9800
@property (nonatomic, readonly) UIColor *warning;

#pragma mark - Gradient Colors

/// Brass gradient colors for backgrounds
@property (nonatomic, readonly) NSArray<UIColor *> *brassGradient;

/// Primary gradient colors
@property (nonatomic, readonly) NSArray<UIColor *> *primaryGradient;

/// Accent gradient colors
@property (nonatomic, readonly) NSArray<UIColor *> *accentGradient;

#pragma mark - Typography

/// Large title font (32pt, bold)
@property (nonatomic, readonly) UIFont *largeTitleFont;

/// Title font (24pt, bold)
@property (nonatomic, readonly) UIFont *titleFont;

/// Subtitle font (20pt, semibold)
@property (nonatomic, readonly) UIFont *subtitleFont;

/// Body font (16pt, regular)
@property (nonatomic, readonly) UIFont *bodyFont;

/// Caption font (14pt, regular)
@property (nonatomic, readonly) UIFont *captionFont;

/// Small caption font (12pt, regular)
@property (nonatomic, readonly) UIFont *smallCaptionFont;

#pragma mark - UI Styling Methods

/// Create a steampunk-styled button with brass background
- (UIButton *)createBrassButtonWithTitle:(NSString *)title;

/// Create a steampunk-styled card view
- (UIView *)createCardViewWithBackgroundColor:(UIColor *)backgroundColor;

/// Create a gradient layer with specified colors
- (CAGradientLayer *)createGradientLayerWithColors:(NSArray<UIColor *> *)colors;

/// Apply steampunk styling to navigation bar
- (void)styleNavigationBar:(UINavigationBar *)navigationBar;

/// Apply steampunk styling to tab bar
- (void)styleTabBar:(UITabBar *)tabBar;

@end

NS_ASSUME_NONNULL_END
