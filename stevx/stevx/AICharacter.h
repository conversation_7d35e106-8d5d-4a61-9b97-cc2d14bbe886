//
//  AICharacter.h
//  SteVx - AI Steampunk Jewelry Makers
//
//  Created by Dev<PERSON>per on 2024/08/01.
//  Copyright © 2024 SteVx. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * AI Character Model
 * Represents a steampunk jewelry making AI expert
 */
@interface AICharacter : NSObject

@property (nonatomic, strong, readonly) NSString *characterId;
@property (nonatomic, strong, readonly) NSString *name;
@property (nonatomic, strong, readonly) NSString *shortDescription;
@property (nonatomic, strong, readonly) NSString *longDescription;
@property (nonatomic, strong, readonly) NSString *iconName;
@property (nonatomic, strong, readonly) NSArray<NSString *> *specialties;
@property (nonatomic, strong, readonly) NSArray<NSString *> *tools;
@property (nonatomic, strong, readonly) NSArray<NSString *> *materials;

/**
 * Initialize AI Character with all properties
 */
- (instancetype)initWithId:(NSString *)characterId
                      name:(NSString *)name
          shortDescription:(NSString *)shortDescription
           longDescription:(NSString *)longDescription
                  iconName:(NSString *)iconName
               specialties:(NSArray<NSString *> *)specialties
                     tools:(NSArray<NSString *> *)tools
                 materials:(NSArray<NSString *> *)materials;

/**
 * Get all available AI characters
 */
+ (NSArray<AICharacter *> *)getAllCharacters;

/**
 * Get character by ID
 */
+ (nullable AICharacter *)getCharacterById:(NSString *)characterId;

/**
 * Get welcome message for this character
 */
- (NSString *)getWelcomeMessage;

/**
 * Generate a response to user message (simple simulation)
 */
- (NSString *)generateResponseToMessage:(NSString *)userMessage;

@end

NS_ASSUME_NONNULL_END
