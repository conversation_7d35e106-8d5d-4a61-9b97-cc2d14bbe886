//
//  MainTabBarController.m
//  SteVx - AI Steampunk Jewelry Makers
//
//  Created by <PERSON><PERSON><PERSON> on 2024/08/01.
//  Copyright © 2024 SteVx. All rights reserved.
//

#import "MainTabBarController.h"
#import "HomeViewController.h"
#import "ProfileViewController.h"
#import "ThemeManager.h"

@interface MainTabBarController ()

@end

@implementation MainTabBarController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self setupViewControllers];
    [self setupTabBarAppearance];
}

- (void)setupViewControllers {
    // Home View Controller
    HomeViewController *homeVC = [[HomeViewController alloc] init];
    UINavigationController *homeNavController = [[UINavigationController alloc] initWithRootViewController:homeVC];
    homeNavController.tabBarItem = [[UITabBarItem alloc] initWithTitle:@"Home" 
                                                                 image:[UIImage systemImageNamed:@"house"] 
                                                         selectedImage:[UIImage systemImageNamed:@"house.fill"]];
    
    // Profile View Controller
    ProfileViewController *profileVC = [[ProfileViewController alloc] init];
    UINavigationController *profileNavController = [[UINavigationController alloc] initWithRootViewController:profileVC];
    profileNavController.tabBarItem = [[UITabBarItem alloc] initWithTitle:@"Profile" 
                                                                     image:[UIImage systemImageNamed:@"person"] 
                                                             selectedImage:[UIImage systemImageNamed:@"person.fill"]];
    
    // Set view controllers
    self.viewControllers = @[homeNavController, profileNavController];
}

- (void)setupTabBarAppearance {
    ThemeManager *theme = [ThemeManager sharedManager];
    
    // Style tab bar
    [theme styleTabBar:self.tabBar];
    
    // Additional tab bar styling
    self.tabBar.layer.shadowColor = theme.primaryDark.CGColor;
    self.tabBar.layer.shadowOffset = CGSizeMake(0, -2);
    self.tabBar.layer.shadowOpacity = 0.1;
    self.tabBar.layer.shadowRadius = 8.0;
}

@end
