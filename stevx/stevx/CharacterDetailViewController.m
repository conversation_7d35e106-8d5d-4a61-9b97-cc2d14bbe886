//
//  CharacterDetailViewController.m
//  SteVx - AI Steampunk Jewelry Makers
//
//  Created by <PERSON><PERSON><PERSON> on 2024/08/01.
//  Copyright © 2024 SteVx. All rights reserved.
//

#import "CharacterDetailViewController.h"
#import "ThemeManager.h"
#import "CartoonAvatarView.h"

@interface ChatMessage : NSObject
@property (nonatomic, strong) NSString *text;
@property (nonatomic, assign) BOOL isUser;
@property (nonatomic, strong) NSDate *timestamp;
@end

@implementation ChatMessage
@end

@interface CharacterDetailViewController () <UITableViewDataSource, UITableViewDelegate, UITextFieldDelegate>

@property (nonatomic, strong) AICharacter *character;
@property (nonatomic, strong) UIScrollView *scrollView;
@property (nonatomic, strong) UIView *contentView;
@property (nonatomic, strong) UIView *characterInfoView;
@property (nonatomic, strong) CartoonAvatarView *characterAvatarView;
@property (nonatomic, strong) UITableView *chatTableView;
@property (nonatomic, strong) UIView *inputContainer;
@property (nonatomic, strong) UITextField *messageTextField;
@property (nonatomic, strong) UIButton *sendButton;
@property (nonatomic, strong) NSMutableArray<ChatMessage *> *messages;

@end

@implementation CharacterDetailViewController

- (instancetype)initWithCharacter:(AICharacter *)character {
    self = [super init];
    if (self) {
        _character = character;
        _messages = [[NSMutableArray alloc] init];
        
        // Add welcome message
        ChatMessage *welcomeMessage = [[ChatMessage alloc] init];
        welcomeMessage.text = [character getWelcomeMessage];
        welcomeMessage.isUser = NO;
        welcomeMessage.timestamp = [NSDate date];
        [_messages addObject:welcomeMessage];
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self setupUI];
    [self setupConstraints];
    [self setupKeyboardNotifications];
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark - Setup Methods

- (void)setupUI {
    ThemeManager *theme = [ThemeManager sharedManager];
    
    self.view.backgroundColor = theme.background;
    self.title = self.character.name;
    
    // Setup scroll view
    self.scrollView = [[UIScrollView alloc] init];
    self.scrollView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.scrollView];
    
    // Content view
    self.contentView = [[UIView alloc] init];
    self.contentView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.scrollView addSubview:self.contentView];
    
    [self setupCharacterInfoView];
    [self setupChatView];
    [self setupInputContainer];
}

- (void)setupCharacterInfoView {
    ThemeManager *theme = [ThemeManager sharedManager];
    
    self.characterInfoView = [theme createCardViewWithBackgroundColor:theme.primaryDark];
    self.characterInfoView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.contentView addSubview:self.characterInfoView];
    
    // Character avatar
    self.characterAvatarView = [[CartoonAvatarView alloc] initWithAvatarType:CartoonAvatarTypeGearMaster];
    self.characterAvatarView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.characterInfoView addSubview:self.characterAvatarView];
    
    // Character name
    UILabel *nameLabel = [[UILabel alloc] init];
    nameLabel.text = self.character.name;
    nameLabel.font = theme.subtitleFont;
    nameLabel.textColor = theme.textWhite;
    nameLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.characterInfoView addSubview:nameLabel];
    
    // Character description
    UILabel *descriptionLabel = [[UILabel alloc] init];
    descriptionLabel.text = self.character.shortDescription;
    descriptionLabel.font = theme.captionFont;
    descriptionLabel.textColor = [theme.textWhite colorWithAlphaComponent:0.8];
    descriptionLabel.numberOfLines = 0;
    descriptionLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.characterInfoView addSubview:descriptionLabel];
    
    // Specialties tags
    UIView *specialtiesContainer = [[UIView alloc] init];
    specialtiesContainer.translatesAutoresizingMaskIntoConstraints = NO;
    [self.characterInfoView addSubview:specialtiesContainer];
    
    CGFloat tagX = 0;
    for (NSString *specialty in self.character.specialties) {
        UILabel *tagLabel = [[UILabel alloc] init];
        tagLabel.text = specialty;
        tagLabel.font = [UIFont systemFontOfSize:12 weight:UIFontWeightMedium];
        tagLabel.textColor = theme.brass;
        tagLabel.backgroundColor = [theme.brass colorWithAlphaComponent:0.2];
        tagLabel.layer.borderColor = theme.brass.CGColor;
        tagLabel.layer.borderWidth = 1;
        tagLabel.layer.cornerRadius = 16;
        tagLabel.textAlignment = NSTextAlignmentCenter;
        tagLabel.translatesAutoresizingMaskIntoConstraints = NO;
        [specialtiesContainer addSubview:tagLabel];
        
        [NSLayoutConstraint activateConstraints:@[
            [tagLabel.leadingAnchor constraintEqualToAnchor:specialtiesContainer.leadingAnchor constant:tagX],
            [tagLabel.topAnchor constraintEqualToAnchor:specialtiesContainer.topAnchor],
            [tagLabel.heightAnchor constraintEqualToConstant:32],
            [tagLabel.widthAnchor constraintEqualToConstant:100]
        ]];
        
        tagX += 108; // 100 + 8 spacing
    }
    
    // Character info constraints
    [NSLayoutConstraint activateConstraints:@[
        [self.characterAvatarView.leadingAnchor constraintEqualToAnchor:self.characterInfoView.leadingAnchor constant:20],
        [self.characterAvatarView.topAnchor constraintEqualToAnchor:self.characterInfoView.topAnchor constant:20],
        [self.characterAvatarView.widthAnchor constraintEqualToConstant:60],
        [self.characterAvatarView.heightAnchor constraintEqualToConstant:60],
        
        [nameLabel.leadingAnchor constraintEqualToAnchor:self.characterAvatarView.trailingAnchor constant:16],
        [nameLabel.topAnchor constraintEqualToAnchor:self.characterInfoView.topAnchor constant:20],
        [nameLabel.trailingAnchor constraintEqualToAnchor:self.characterInfoView.trailingAnchor constant:-20],
        
        [descriptionLabel.leadingAnchor constraintEqualToAnchor:self.characterAvatarView.trailingAnchor constant:16],
        [descriptionLabel.topAnchor constraintEqualToAnchor:nameLabel.bottomAnchor constant:8],
        [descriptionLabel.trailingAnchor constraintEqualToAnchor:self.characterInfoView.trailingAnchor constant:-20],
        
        [specialtiesContainer.topAnchor constraintEqualToAnchor:self.characterAvatarView.bottomAnchor constant:16],
        [specialtiesContainer.leadingAnchor constraintEqualToAnchor:self.characterInfoView.leadingAnchor constant:20],
        [specialtiesContainer.trailingAnchor constraintEqualToAnchor:self.characterInfoView.trailingAnchor constant:-20],
        [specialtiesContainer.heightAnchor constraintEqualToConstant:32],
        [specialtiesContainer.bottomAnchor constraintEqualToAnchor:self.characterInfoView.bottomAnchor constant:-20]
    ]];
}

- (void)setupChatView {
    ThemeManager *theme = [ThemeManager sharedManager];
    
    // Chat container
    UIView *chatContainer = [theme createCardViewWithBackgroundColor:theme.primaryDark];
    chatContainer.translatesAutoresizingMaskIntoConstraints = NO;
    [self.contentView addSubview:chatContainer];
    
    // Chat title
    UILabel *chatTitleLabel = [[UILabel alloc] init];
    chatTitleLabel.text = @"💬 Chat with AI Expert";
    chatTitleLabel.font = theme.bodyFont;
    chatTitleLabel.textColor = theme.textWhite;
    chatTitleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [chatContainer addSubview:chatTitleLabel];
    
    // Chat table view
    self.chatTableView = [[UITableView alloc] init];
    self.chatTableView.translatesAutoresizingMaskIntoConstraints = NO;
    self.chatTableView.backgroundColor = [UIColor clearColor];
    self.chatTableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    self.chatTableView.dataSource = self;
    self.chatTableView.delegate = self;
    [self.chatTableView registerClass:[ChatMessageTableViewCell class] forCellReuseIdentifier:@"ChatCell"];
    [chatContainer addSubview:self.chatTableView];
    
    [NSLayoutConstraint activateConstraints:@[
        [chatContainer.topAnchor constraintEqualToAnchor:self.characterInfoView.bottomAnchor constant:20],
        [chatContainer.leadingAnchor constraintEqualToAnchor:self.contentView.leadingAnchor constant:20],
        [chatContainer.trailingAnchor constraintEqualToAnchor:self.contentView.trailingAnchor constant:-20],
        [chatContainer.heightAnchor constraintEqualToConstant:400],
        
        [chatTitleLabel.topAnchor constraintEqualToAnchor:chatContainer.topAnchor constant:16],
        [chatTitleLabel.leadingAnchor constraintEqualToAnchor:chatContainer.leadingAnchor constant:16],
        [chatTitleLabel.trailingAnchor constraintEqualToAnchor:chatContainer.trailingAnchor constant:-16],
        
        [self.chatTableView.topAnchor constraintEqualToAnchor:chatTitleLabel.bottomAnchor constant:16],
        [self.chatTableView.leadingAnchor constraintEqualToAnchor:chatContainer.leadingAnchor constant:16],
        [self.chatTableView.trailingAnchor constraintEqualToAnchor:chatContainer.trailingAnchor constant:-16],
        [self.chatTableView.bottomAnchor constraintEqualToAnchor:chatContainer.bottomAnchor constant:-16]
    ]];
}

- (void)setupInputContainer {
    ThemeManager *theme = [ThemeManager sharedManager];
    
    self.inputContainer = [[UIView alloc] init];
    self.inputContainer.translatesAutoresizingMaskIntoConstraints = NO;
    self.inputContainer.backgroundColor = theme.primaryDark;
    self.inputContainer.layer.cornerRadius = 16;
    self.inputContainer.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
    [self.view addSubview:self.inputContainer];
    
    // Message text field
    self.messageTextField = [[UITextField alloc] init];
    self.messageTextField.placeholder = @"Type your question...";
    self.messageTextField.backgroundColor = [theme.textWhite colorWithAlphaComponent:0.1];
    self.messageTextField.textColor = theme.textWhite;
    self.messageTextField.layer.cornerRadius = 24;
    self.messageTextField.leftView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 16, 0)];
    self.messageTextField.leftViewMode = UITextFieldViewModeAlways;
    self.messageTextField.delegate = self;
    self.messageTextField.translatesAutoresizingMaskIntoConstraints = NO;
    [self.inputContainer addSubview:self.messageTextField];
    
    // Send button
    self.sendButton = [[UIButton alloc] init];
    self.sendButton.backgroundColor = theme.brass;
    self.sendButton.layer.cornerRadius = 24;
    [self.sendButton setTitle:@"➤" forState:UIControlStateNormal];
    [self.sendButton setTitleColor:theme.textWhite forState:UIControlStateNormal];
    [self.sendButton addTarget:self action:@selector(sendMessage) forControlEvents:UIControlEventTouchUpInside];
    self.sendButton.translatesAutoresizingMaskIntoConstraints = NO;
    [self.inputContainer addSubview:self.sendButton];
}

- (void)setupConstraints {
    [NSLayoutConstraint activateConstraints:@[
        // Scroll view
        [self.scrollView.topAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.topAnchor],
        [self.scrollView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [self.scrollView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [self.scrollView.bottomAnchor constraintEqualToAnchor:self.inputContainer.topAnchor],
        
        // Content view
        [self.contentView.topAnchor constraintEqualToAnchor:self.scrollView.topAnchor],
        [self.contentView.leadingAnchor constraintEqualToAnchor:self.scrollView.leadingAnchor],
        [self.contentView.trailingAnchor constraintEqualToAnchor:self.scrollView.trailingAnchor],
        [self.contentView.bottomAnchor constraintEqualToAnchor:self.scrollView.bottomAnchor],
        [self.contentView.widthAnchor constraintEqualToAnchor:self.scrollView.widthAnchor],
        
        // Character info view
        [self.characterInfoView.topAnchor constraintEqualToAnchor:self.contentView.topAnchor constant:20],
        [self.characterInfoView.leadingAnchor constraintEqualToAnchor:self.contentView.leadingAnchor constant:20],
        [self.characterInfoView.trailingAnchor constraintEqualToAnchor:self.contentView.trailingAnchor constant:-20],
        
        // Input container
        [self.inputContainer.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [self.inputContainer.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [self.inputContainer.bottomAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.bottomAnchor],
        [self.inputContainer.heightAnchor constraintEqualToConstant:80],
        
        // Message text field
        [self.messageTextField.leadingAnchor constraintEqualToAnchor:self.inputContainer.leadingAnchor constant:16],
        [self.messageTextField.trailingAnchor constraintEqualToAnchor:self.sendButton.leadingAnchor constant:-12],
        [self.messageTextField.centerYAnchor constraintEqualToAnchor:self.inputContainer.centerYAnchor],
        [self.messageTextField.heightAnchor constraintEqualToConstant:48],
        
        // Send button
        [self.sendButton.trailingAnchor constraintEqualToAnchor:self.inputContainer.trailingAnchor constant:-16],
        [self.sendButton.centerYAnchor constraintEqualToAnchor:self.inputContainer.centerYAnchor],
        [self.sendButton.widthAnchor constraintEqualToConstant:48],
        [self.sendButton.heightAnchor constraintEqualToConstant:48]
    ]];
}

- (void)setupKeyboardNotifications {
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(keyboardWillShow:)
                                                 name:UIKeyboardWillShowNotification
                                               object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(keyboardWillHide:)
                                                 name:UIKeyboardWillHideNotification
                                               object:nil];
}

#pragma mark - Actions

- (void)sendMessage {
    NSString *messageText = [self.messageTextField.text stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
    
    if (messageText.length == 0) {
        return;
    }
    
    // Add user message
    ChatMessage *userMessage = [[ChatMessage alloc] init];
    userMessage.text = messageText;
    userMessage.isUser = YES;
    userMessage.timestamp = [NSDate date];
    [self.messages addObject:userMessage];
    
    // Add AI response
    ChatMessage *aiResponse = [[ChatMessage alloc] init];
    aiResponse.text = [self.character generateResponseToMessage:messageText];
    aiResponse.isUser = NO;
    aiResponse.timestamp = [NSDate date];
    [self.messages addObject:aiResponse];
    
    // Clear text field
    self.messageTextField.text = @"";
    
    // Reload table view
    [self.chatTableView reloadData];
    
    // Scroll to bottom
    if (self.messages.count > 0) {
        NSIndexPath *lastIndexPath = [NSIndexPath indexPathForRow:self.messages.count - 1 inSection:0];
        [self.chatTableView scrollToRowAtIndexPath:lastIndexPath atScrollPosition:UITableViewScrollPositionBottom animated:YES];
    }
}

#pragma mark - Keyboard Notifications

- (void)keyboardWillShow:(NSNotification *)notification {
    // Handle keyboard appearance
}

- (void)keyboardWillHide:(NSNotification *)notification {
    // Handle keyboard dismissal
}

#pragma mark - UITableViewDataSource

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.messages.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    ChatMessageTableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"ChatCell" forIndexPath:indexPath];
    
    ChatMessage *message = self.messages[indexPath.row];
    [cell configureWithMessage:message];
    
    return cell;
}

#pragma mark - UITextFieldDelegate

- (BOOL)textFieldShouldReturn:(UITextField *)textField {
    [self sendMessage];
    return YES;
}

@end

#pragma mark - ChatMessageTableViewCell

@interface ChatMessageTableViewCell : UITableViewCell

- (void)configureWithMessage:(ChatMessage *)message;

@end

@implementation ChatMessageTableViewCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.backgroundColor = [UIColor clearColor];
        self.selectionStyle = UITableViewCellSelectionStyleNone;
    }
    return self;
}

- (void)configureWithMessage:(ChatMessage *)message {
    // Remove existing subviews
    for (UIView *subview in self.contentView.subviews) {
        [subview removeFromSuperview];
    }
    
    ThemeManager *theme = [ThemeManager sharedManager];
    
    // Create message bubble
    UIView *bubbleView = [[UIView alloc] init];
    bubbleView.translatesAutoresizingMaskIntoConstraints = NO;
    bubbleView.backgroundColor = message.isUser ? theme.brass : [theme.textWhite colorWithAlphaComponent:0.1];
    bubbleView.layer.cornerRadius = 12;
    [self.contentView addSubview:bubbleView];
    
    // Message label
    UILabel *messageLabel = [[UILabel alloc] init];
    messageLabel.text = message.text;
    messageLabel.font = [UIFont systemFontOfSize:14];
    messageLabel.textColor = theme.textWhite;
    messageLabel.numberOfLines = 0;
    messageLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [bubbleView addSubview:messageLabel];
    
    // Constraints
    if (message.isUser) {
        [NSLayoutConstraint activateConstraints:@[
            [bubbleView.trailingAnchor constraintEqualToAnchor:self.contentView.trailingAnchor constant:-8],
            [bubbleView.topAnchor constraintEqualToAnchor:self.contentView.topAnchor constant:4],
            [bubbleView.bottomAnchor constraintEqualToAnchor:self.contentView.bottomAnchor constant:-4],
            [bubbleView.widthAnchor constraintLessThanOrEqualToAnchor:self.contentView.widthAnchor multiplier:0.7],
            
            [messageLabel.topAnchor constraintEqualToAnchor:bubbleView.topAnchor constant:12],
            [messageLabel.leadingAnchor constraintEqualToAnchor:bubbleView.leadingAnchor constant:12],
            [messageLabel.trailingAnchor constraintEqualToAnchor:bubbleView.trailingAnchor constant:-12],
            [messageLabel.bottomAnchor constraintEqualToAnchor:bubbleView.bottomAnchor constant:-12]
        ]];
    } else {
        [NSLayoutConstraint activateConstraints:@[
            [bubbleView.leadingAnchor constraintEqualToAnchor:self.contentView.leadingAnchor constant:8],
            [bubbleView.topAnchor constraintEqualToAnchor:self.contentView.topAnchor constant:4],
            [bubbleView.bottomAnchor constraintEqualToAnchor:self.contentView.bottomAnchor constant:-4],
            [bubbleView.widthAnchor constraintLessThanOrEqualToAnchor:self.contentView.widthAnchor multiplier:0.7],
            
            [messageLabel.topAnchor constraintEqualToAnchor:bubbleView.topAnchor constant:12],
            [messageLabel.leadingAnchor constraintEqualToAnchor:bubbleView.leadingAnchor constant:12],
            [messageLabel.trailingAnchor constraintEqualToAnchor:bubbleView.trailingAnchor constant:-12],
            [messageLabel.bottomAnchor constraintEqualToAnchor:bubbleView.bottomAnchor constant:-12]
        ]];
    }
}

@end
