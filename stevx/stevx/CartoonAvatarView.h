//
//  CartoonAvatarView.h
//  SteVx - AI Steampunk Jewelry Makers
//
//  Created by <PERSON><PERSON><PERSON> on 2024/08/01.
//  Copyright © 2024 SteVx. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSI<PERSON><PERSON>, CartoonAvatarType) {
    CartoonAvatarTypeUser,
    CartoonAvatarTypeGearMaster,
    CartoonAvatarTypeSteampunkLady,
    CartoonAvatarTypeInventor,
    CartoonAvatarTypeCrafter
};

/**
 * Cartoon Avatar View
 * Custom UIView that draws steampunk-style cartoon avatars using Core Graphics
 */
@interface CartoonAvatarView : UIView

@property (nonatomic, assign) CartoonAvatarType avatarType;
@property (nonatomic, assign) BOOL showBorder;
@property (nonatomic, strong) UIColor *borderColor;
@property (nonatomic, assign) CGFloat borderWidth;

/**
 * Initialize with avatar type
 */
- (instancetype)initWithAvatarType:(CartoonAvatarType)avatarType;

/**
 * Initialize with frame and avatar type
 */
- (instancetype)initWithFrame:(CGRect)frame avatarType:(CartoonAvatarType)avatarType;

/**
 * Update avatar type and redraw
 */
- (void)setAvatarType:(CartoonAvatarType)avatarType animated:(BOOL)animated;

@end

NS_ASSUME_NONNULL_END
