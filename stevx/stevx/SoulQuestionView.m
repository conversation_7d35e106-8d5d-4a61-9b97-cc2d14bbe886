//
//  SoulQuestionView.m
//  SteVx - AI Steampunk Jewelry Makers
//
//  Created by Developer on 2024/08/01.
//  Copyright © 2024 SteVx. All rights reserved.
//

#import "SoulQuestionView.h"
#import "ThemeManager.h"
#import "SoulQuestion.h"

@interface SoulQuestionView () <UITextViewDelegate>

@property (nonatomic, strong) UIView *headerView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *questionLabel;
@property (nonatomic, strong) UILabel *rewardLabel;
@property (nonatomic, strong) UIButton *startButton;
@property (nonatomic, strong) UITextView *answerTextView;
@property (nonatomic, strong) UIButton *submitButton;
@property (nonatomic, strong) UIButton *cancelButton;
@property (nonatomic, strong) UIView *completedView;
@property (nonatomic, strong) SoulQuestion *currentQuestion;
@property (nonatomic, assign) BOOL isAnswering;

@end

@implementation SoulQuestionView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupSoulQuestionView];
    }
    return self;
}

- (void)setupSoulQuestionView {
    ThemeManager *theme = [ThemeManager sharedManager];
    
    self.backgroundColor = theme.primaryDark;
    self.layer.cornerRadius = 16;
    self.layer.masksToBounds = YES;
    
    [self setupHeaderView];
    [self setupQuestionView];
    [self setupAnswerView];
    [self setupCompletedView];
    [self refreshView];
}

- (void)setupHeaderView {
    ThemeManager *theme = [ThemeManager sharedManager];
    
    self.headerView = [[UIView alloc] init];
    self.headerView.translatesAutoresizingMaskIntoConstraints = NO;
    [self addSubview:self.headerView];
    
    // Icon
    UIView *iconContainer = [[UIView alloc] init];
    iconContainer.backgroundColor = theme.brass;
    iconContainer.layer.cornerRadius = 20;
    iconContainer.translatesAutoresizingMaskIntoConstraints = NO;
    [self.headerView addSubview:iconContainer];
    
    UILabel *iconLabel = [[UILabel alloc] init];
    iconLabel.text = @"🧠";
    iconLabel.font = [UIFont systemFontOfSize:20];
    iconLabel.textAlignment = NSTextAlignmentCenter;
    iconLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [iconContainer addSubview:iconLabel];
    
    // Title
    self.titleLabel = [[UILabel alloc] init];
    self.titleLabel.text = @"AI Soul Question";
    self.titleLabel.font = [UIFont boldSystemFontOfSize:18];
    self.titleLabel.textColor = theme.textWhite;
    self.titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.headerView addSubview:self.titleLabel];
    
    // Info button
    UIButton *infoButton = [[UIButton alloc] init];
    [infoButton setTitle:@"ℹ️" forState:UIControlStateNormal];
    [infoButton addTarget:self action:@selector(showInfoAlert) forControlEvents:UIControlEventTouchUpInside];
    infoButton.translatesAutoresizingMaskIntoConstraints = NO;
    [self.headerView addSubview:infoButton];
    
    [NSLayoutConstraint activateConstraints:@[
        [self.headerView.topAnchor constraintEqualToAnchor:self.topAnchor constant:16],
        [self.headerView.leadingAnchor constraintEqualToAnchor:self.leadingAnchor constant:16],
        [self.headerView.trailingAnchor constraintEqualToAnchor:self.trailingAnchor constant:-16],
        [self.headerView.heightAnchor constraintEqualToConstant:40],
        
        [iconContainer.leadingAnchor constraintEqualToAnchor:self.headerView.leadingAnchor],
        [iconContainer.centerYAnchor constraintEqualToAnchor:self.headerView.centerYAnchor],
        [iconContainer.widthAnchor constraintEqualToConstant:40],
        [iconContainer.heightAnchor constraintEqualToConstant:40],
        
        [iconLabel.centerXAnchor constraintEqualToAnchor:iconContainer.centerXAnchor],
        [iconLabel.centerYAnchor constraintEqualToAnchor:iconContainer.centerYAnchor],
        
        [self.titleLabel.leadingAnchor constraintEqualToAnchor:iconContainer.trailingAnchor constant:12],
        [self.titleLabel.centerYAnchor constraintEqualToAnchor:self.headerView.centerYAnchor],
        
        [infoButton.trailingAnchor constraintEqualToAnchor:self.headerView.trailingAnchor],
        [infoButton.centerYAnchor constraintEqualToAnchor:self.headerView.centerYAnchor],
        [infoButton.widthAnchor constraintEqualToConstant:30],
        [infoButton.heightAnchor constraintEqualToConstant:30]
    ]];
}

- (void)setupQuestionView {
    ThemeManager *theme = [ThemeManager sharedManager];
    
    // Question label
    self.questionLabel = [[UILabel alloc] init];
    self.questionLabel.font = theme.bodyFont;
    self.questionLabel.textColor = theme.textWhite;
    self.questionLabel.numberOfLines = 0;
    self.questionLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self addSubview:self.questionLabel];
    
    // Reward label
    self.rewardLabel = [[UILabel alloc] init];
    self.rewardLabel.font = theme.captionFont;
    self.rewardLabel.textColor = theme.textWhite;
    self.rewardLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self addSubview:self.rewardLabel];
    
    // Start button
    self.startButton = [[ThemeManager sharedManager] createBrassButtonWithTitle:@"Start Answer"];
    [self.startButton addTarget:self action:@selector(startAnswering) forControlEvents:UIControlEventTouchUpInside];
    self.startButton.translatesAutoresizingMaskIntoConstraints = NO;
    [self addSubview:self.startButton];
    
    [NSLayoutConstraint activateConstraints:@[
        [self.questionLabel.topAnchor constraintEqualToAnchor:self.headerView.bottomAnchor constant:16],
        [self.questionLabel.leadingAnchor constraintEqualToAnchor:self.leadingAnchor constant:16],
        [self.questionLabel.trailingAnchor constraintEqualToAnchor:self.trailingAnchor constant:-16],
        
        [self.rewardLabel.topAnchor constraintEqualToAnchor:self.questionLabel.bottomAnchor constant:12],
        [self.rewardLabel.leadingAnchor constraintEqualToAnchor:self.leadingAnchor constant:16],
        [self.rewardLabel.trailingAnchor constraintEqualToAnchor:self.trailingAnchor constant:-16],
        
        [self.startButton.topAnchor constraintEqualToAnchor:self.rewardLabel.bottomAnchor constant:16],
        [self.startButton.centerXAnchor constraintEqualToAnchor:self.centerXAnchor],
        [self.startButton.widthAnchor constraintEqualToConstant:120],
        [self.startButton.heightAnchor constraintEqualToConstant:40]
    ]];
}

- (void)setupAnswerView {
    ThemeManager *theme = [ThemeManager sharedManager];
    
    // Answer text view
    self.answerTextView = [[UITextView alloc] init];
    self.answerTextView.backgroundColor = [theme.textWhite colorWithAlphaComponent:0.1];
    self.answerTextView.textColor = theme.textWhite;
    self.answerTextView.font = theme.bodyFont;
    self.answerTextView.layer.cornerRadius = 12;
    self.answerTextView.layer.borderWidth = 1;
    self.answerTextView.layer.borderColor = [theme.textWhite colorWithAlphaComponent:0.3].CGColor;
    self.answerTextView.delegate = self;
    self.answerTextView.translatesAutoresizingMaskIntoConstraints = NO;
    self.answerTextView.hidden = YES;
    [self addSubview:self.answerTextView];
    
    // Button container
    UIView *buttonContainer = [[UIView alloc] init];
    buttonContainer.translatesAutoresizingMaskIntoConstraints = NO;
    buttonContainer.hidden = YES;
    [self addSubview:buttonContainer];
    
    // Submit button
    self.submitButton = [[ThemeManager sharedManager] createBrassButtonWithTitle:@"Submit Answer"];
    [self.submitButton addTarget:self action:@selector(submitAnswer) forControlEvents:UIControlEventTouchUpInside];
    self.submitButton.translatesAutoresizingMaskIntoConstraints = NO;
    [buttonContainer addSubview:self.submitButton];
    
    // Cancel button
    self.cancelButton = [[UIButton alloc] init];
    [self.cancelButton setTitle:@"Cancel" forState:UIControlStateNormal];
    [self.cancelButton setTitleColor:theme.textWhite forState:UIControlStateNormal];
    self.cancelButton.backgroundColor = [theme.textWhite colorWithAlphaComponent:0.2];
    self.cancelButton.layer.cornerRadius = 20;
    [self.cancelButton addTarget:self action:@selector(cancelAnswering) forControlEvents:UIControlEventTouchUpInside];
    self.cancelButton.translatesAutoresizingMaskIntoConstraints = NO;
    [buttonContainer addSubview:self.cancelButton];
    
    [NSLayoutConstraint activateConstraints:@[
        [self.answerTextView.topAnchor constraintEqualToAnchor:self.headerView.bottomAnchor constant:16],
        [self.answerTextView.leadingAnchor constraintEqualToAnchor:self.leadingAnchor constant:16],
        [self.answerTextView.trailingAnchor constraintEqualToAnchor:self.trailingAnchor constant:-16],
        [self.answerTextView.heightAnchor constraintEqualToConstant:100],
        
        [buttonContainer.topAnchor constraintEqualToAnchor:self.answerTextView.bottomAnchor constant:16],
        [buttonContainer.leadingAnchor constraintEqualToAnchor:self.leadingAnchor constant:16],
        [buttonContainer.trailingAnchor constraintEqualToAnchor:self.trailingAnchor constant:-16],
        [buttonContainer.heightAnchor constraintEqualToConstant:40],
        
        [self.submitButton.leadingAnchor constraintEqualToAnchor:buttonContainer.leadingAnchor],
        [self.submitButton.topAnchor constraintEqualToAnchor:buttonContainer.topAnchor],
        [self.submitButton.bottomAnchor constraintEqualToAnchor:buttonContainer.bottomAnchor],
        [self.submitButton.trailingAnchor constraintEqualToAnchor:self.cancelButton.leadingAnchor constant:-12],
        
        [self.cancelButton.trailingAnchor constraintEqualToAnchor:buttonContainer.trailingAnchor],
        [self.cancelButton.topAnchor constraintEqualToAnchor:buttonContainer.topAnchor],
        [self.cancelButton.bottomAnchor constraintEqualToAnchor:buttonContainer.bottomAnchor],
        [self.cancelButton.widthAnchor constraintEqualToConstant:80]
    ]];
}

- (void)setupCompletedView {
    ThemeManager *theme = [ThemeManager sharedManager];
    
    self.completedView = [[UIView alloc] init];
    self.completedView.translatesAutoresizingMaskIntoConstraints = NO;
    self.completedView.hidden = YES;
    [self addSubview:self.completedView];
    
    UILabel *completedLabel = [[UILabel alloc] init];
    completedLabel.text = @"Today's Question Completed! ✅";
    completedLabel.font = [UIFont boldSystemFontOfSize:16];
    completedLabel.textColor = theme.textWhite;
    completedLabel.textAlignment = NSTextAlignmentCenter;
    completedLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.completedView addSubview:completedLabel];
    
    UILabel *messageLabel = [[UILabel alloc] init];
    messageLabel.text = @"Come back tomorrow for a new soul question";
    messageLabel.font = theme.captionFont;
    messageLabel.textColor = theme.textWhite;
    messageLabel.textAlignment = NSTextAlignmentCenter;
    messageLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.completedView addSubview:messageLabel];
    
    [NSLayoutConstraint activateConstraints:@[
        [self.completedView.topAnchor constraintEqualToAnchor:self.headerView.bottomAnchor constant:16],
        [self.completedView.leadingAnchor constraintEqualToAnchor:self.leadingAnchor constant:16],
        [self.completedView.trailingAnchor constraintEqualToAnchor:self.trailingAnchor constant:-16],
        [self.completedView.bottomAnchor constraintEqualToAnchor:self.bottomAnchor constant:-16],
        
        [completedLabel.topAnchor constraintEqualToAnchor:self.completedView.topAnchor],
        [completedLabel.leadingAnchor constraintEqualToAnchor:self.completedView.leadingAnchor],
        [completedLabel.trailingAnchor constraintEqualToAnchor:self.completedView.trailingAnchor],
        
        [messageLabel.topAnchor constraintEqualToAnchor:completedLabel.bottomAnchor constant:8],
        [messageLabel.leadingAnchor constraintEqualToAnchor:self.completedView.leadingAnchor],
        [messageLabel.trailingAnchor constraintEqualToAnchor:self.completedView.trailingAnchor]
    ]];
}

- (void)refreshView {
    BOOL hasAnswered = [SoulQuestion hasAnsweredTodaysQuestion];
    
    if (hasAnswered) {
        [self showCompletedState];
    } else {
        [self showQuestionState];
    }
}

- (void)showQuestionState {
    self.currentQuestion = [SoulQuestion getTodaysQuestion];
    
    self.questionLabel.text = self.currentQuestion.question;
    self.rewardLabel.text = [NSString stringWithFormat:@"Answer to earn %ld coins", (long)self.currentQuestion.reward];
    
    self.questionLabel.hidden = NO;
    self.rewardLabel.hidden = NO;
    self.startButton.hidden = NO;
    self.answerTextView.hidden = YES;
    self.completedView.hidden = YES;
    
    // Hide answer view buttons
    for (UIView *subview in self.subviews) {
        if ([subview isKindOfClass:[UIView class]] && subview != self.headerView && 
            subview != self.questionLabel && subview != self.rewardLabel && 
            subview != self.startButton && subview != self.completedView) {
            subview.hidden = YES;
        }
    }
}

- (void)showAnswerState {
    self.questionLabel.hidden = YES;
    self.rewardLabel.hidden = YES;
    self.startButton.hidden = YES;
    self.answerTextView.hidden = NO;
    self.completedView.hidden = YES;
    
    // Show answer view buttons
    for (UIView *subview in self.subviews) {
        if ([subview isKindOfClass:[UIView class]] && subview != self.headerView && 
            subview != self.questionLabel && subview != self.rewardLabel && 
            subview != self.startButton && subview != self.completedView) {
            subview.hidden = NO;
        }
    }
    
    [self.answerTextView becomeFirstResponder];
}

- (void)showCompletedState {
    self.questionLabel.hidden = YES;
    self.rewardLabel.hidden = YES;
    self.startButton.hidden = YES;
    self.answerTextView.hidden = YES;
    self.completedView.hidden = NO;
    
    // Hide answer view buttons
    for (UIView *subview in self.subviews) {
        if ([subview isKindOfClass:[UIView class]] && subview != self.headerView && 
            subview != self.completedView) {
            subview.hidden = YES;
        }
    }
}

#pragma mark - Actions

- (void)startAnswering {
    self.isAnswering = YES;
    [self showAnswerState];
}

- (void)submitAnswer {
    NSString *answer = [self.answerTextView.text stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
    
    if (answer.length == 0) {
        [self showAlertWithTitle:@"Empty Answer" message:@"Please write your thoughts before submitting."];
        return;
    }
    
    // Mark as answered and add coins
    [SoulQuestion markTodaysQuestionAsAnswered];
    [SoulQuestion addCoins:self.currentQuestion.reward];
    
    // Show success alert
    [self showSuccessAlertWithReward:self.currentQuestion.reward];
    
    // Update view
    [self showCompletedState];
    
    // Notify delegate
    if (self.delegate && [self.delegate respondsToSelector:@selector(soulQuestionViewDidCompleteQuestion:)]) {
        [self.delegate soulQuestionViewDidCompleteQuestion:self.currentQuestion.reward];
    }
}

- (void)cancelAnswering {
    self.isAnswering = NO;
    self.answerTextView.text = @"";
    [self.answerTextView resignFirstResponder];
    [self showQuestionState];
}

- (void)showInfoAlert {
    [self showAlertWithTitle:@"AI Soul Question" 
                     message:@"Every day you can get a random soul question. Answer it to earn 10 coins reward. These questions will help you better understand steampunk culture and jewelry crafting art."];
}

- (void)showSuccessAlertWithReward:(NSInteger)reward {
    [self showAlertWithTitle:@"Congratulations! 🎉" 
                     message:[NSString stringWithFormat:@"You earned %ld coins reward!", (long)reward]];
}

- (void)showAlertWithTitle:(NSString *)title message:(NSString *)message {
    UIAlertController *alert = [UIAlertController alertControllerWithTitle:title 
                                                                   message:message 
                                                            preferredStyle:UIAlertControllerStyleAlert];
    
    UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"Got it" 
                                                       style:UIAlertActionStyleDefault 
                                                     handler:nil];
    [alert addAction:okAction];
    
    // Find the view controller to present from
    UIViewController *presentingVC = [self findViewController];
    if (presentingVC) {
        [presentingVC presentViewController:alert animated:YES completion:nil];
    }
}

- (UIViewController *)findViewController {
    UIResponder *responder = self;
    while (responder) {
        if ([responder isKindOfClass:[UIViewController class]]) {
            return (UIViewController *)responder;
        }
        responder = [responder nextResponder];
    }
    return nil;
}

@end
