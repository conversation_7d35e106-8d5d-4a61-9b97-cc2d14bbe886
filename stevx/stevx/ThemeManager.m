//
//  ThemeManager.m
//  SteVx - AI Steampunk Jewelry Makers
//
//  Created by Developer on 2024/08/01.
//  Copyright © 2024 SteVx. All rights reserved.
//

#import "ThemeManager.h"

@implementation ThemeManager

+ (instancetype)sharedManager {
    static ThemeManager *sharedInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        sharedInstance = [[ThemeManager alloc] init];
    });
    return sharedInstance;
}

#pragma mark - Color Properties

- (UIColor *)primaryDark {
    return [UIColor colorWithRed:44.0/255.0 green:44.0/255.0 blue:44.0/255.0 alpha:1.0];
}

- (UIColor *)brass {
    return [UIColor colorWithRed:197.0/255.0 green:139.0/255.0 blue:67.0/255.0 alpha:1.0];
}

- (UIColor *)accent {
    return [UIColor colorWithRed:139.0/255.0 green:0.0/255.0 blue:0.0/255.0 alpha:1.0];
}

- (UIColor *)background {
    return [UIColor colorWithRed:247.0/255.0 green:242.0/255.0 blue:232.0/255.0 alpha:1.0];
}

- (UIColor *)textWhite {
    return [UIColor whiteColor];
}

- (UIColor *)textDark {
    return [UIColor colorWithRed:44.0/255.0 green:44.0/255.0 blue:44.0/255.0 alpha:1.0];
}

- (UIColor *)overlay {
    return [UIColor colorWithRed:0.0 green:0.0 blue:0.0 alpha:0.5];
}

- (UIColor *)success {
    return [UIColor colorWithRed:76.0/255.0 green:175.0/255.0 blue:80.0/255.0 alpha:1.0];
}

- (UIColor *)warning {
    return [UIColor colorWithRed:255.0/255.0 green:152.0/255.0 blue:0.0/255.0 alpha:1.0];
}

#pragma mark - Gradient Colors

- (NSArray<UIColor *> *)brassGradient {
    return @[
        [self.brass colorWithAlphaComponent:0.8],
        self.brass,
        self.primaryDark
    ];
}

- (NSArray<UIColor *> *)primaryGradient {
    return @[
        self.primaryDark,
        self.background
    ];
}

- (NSArray<UIColor *> *)accentGradient {
    return @[
        [self.accent colorWithAlphaComponent:0.7],
        self.primaryDark
    ];
}

#pragma mark - Typography

- (UIFont *)largeTitleFont {
    return [UIFont boldSystemFontOfSize:32.0];
}

- (UIFont *)titleFont {
    return [UIFont boldSystemFontOfSize:24.0];
}

- (UIFont *)subtitleFont {
    return [UIFont systemFontOfSize:20.0 weight:UIFontWeightSemibold];
}

- (UIFont *)bodyFont {
    return [UIFont systemFontOfSize:16.0];
}

- (UIFont *)captionFont {
    return [UIFont systemFontOfSize:14.0];
}

- (UIFont *)smallCaptionFont {
    return [UIFont systemFontOfSize:12.0];
}

#pragma mark - UI Styling Methods

- (UIButton *)createBrassButtonWithTitle:(NSString *)title {
    UIButton *button = [UIButton buttonWithType:UIButtonTypeSystem];
    [button setTitle:title forState:UIControlStateNormal];
    [button setTitleColor:self.textWhite forState:UIControlStateNormal];
    button.titleLabel.font = [UIFont boldSystemFontOfSize:16.0];
    button.backgroundColor = self.brass;
    button.layer.cornerRadius = 24.0;
    button.layer.masksToBounds = YES;
    
    // Add shadow
    button.layer.shadowColor = self.primaryDark.CGColor;
    button.layer.shadowOffset = CGSizeMake(0, 4);
    button.layer.shadowOpacity = 0.3;
    button.layer.shadowRadius = 8.0;
    button.layer.masksToBounds = NO;
    
    return button;
}

- (UIView *)createCardViewWithBackgroundColor:(UIColor *)backgroundColor {
    UIView *cardView = [[UIView alloc] init];
    cardView.backgroundColor = backgroundColor;
    cardView.layer.cornerRadius = 16.0;
    cardView.layer.masksToBounds = YES;
    
    // Add border
    cardView.layer.borderWidth = 2.0;
    cardView.layer.borderColor = self.primaryDark.CGColor;
    
    // Add shadow
    cardView.layer.shadowColor = self.primaryDark.CGColor;
    cardView.layer.shadowOffset = CGSizeMake(0, 4);
    cardView.layer.shadowOpacity = 0.1;
    cardView.layer.shadowRadius = 8.0;
    cardView.layer.masksToBounds = NO;
    
    return cardView;
}

- (CAGradientLayer *)createGradientLayerWithColors:(NSArray<UIColor *> *)colors {
    CAGradientLayer *gradientLayer = [CAGradientLayer layer];
    
    NSMutableArray *cgColors = [NSMutableArray array];
    for (UIColor *color in colors) {
        [cgColors addObject:(id)color.CGColor];
    }
    
    gradientLayer.colors = cgColors;
    gradientLayer.startPoint = CGPointMake(0.0, 0.0);
    gradientLayer.endPoint = CGPointMake(1.0, 1.0);
    
    return gradientLayer;
}

- (void)styleNavigationBar:(UINavigationBar *)navigationBar {
    navigationBar.backgroundColor = self.primaryDark;
    navigationBar.barTintColor = self.primaryDark;
    navigationBar.tintColor = self.textWhite;
    navigationBar.titleTextAttributes = @{
        NSForegroundColorAttributeName: self.textWhite,
        NSFontAttributeName: self.titleFont
    };
    
    if (@available(iOS 13.0, *)) {
        UINavigationBarAppearance *appearance = [[UINavigationBarAppearance alloc] init];
        [appearance configureWithOpaqueBackground];
        appearance.backgroundColor = self.primaryDark;
        appearance.titleTextAttributes = @{
            NSForegroundColorAttributeName: self.textWhite,
            NSFontAttributeName: self.titleFont
        };
        navigationBar.standardAppearance = appearance;
        navigationBar.scrollEdgeAppearance = appearance;
    }
}

- (void)styleTabBar:(UITabBar *)tabBar {
    tabBar.backgroundColor = self.primaryDark;
    tabBar.barTintColor = self.primaryDark;
    tabBar.tintColor = self.brass;
    tabBar.unselectedItemTintColor = self.textWhite;
    
    if (@available(iOS 13.0, *)) {
        UITabBarAppearance *appearance = [[UITabBarAppearance alloc] init];
        [appearance configureWithOpaqueBackground];
        appearance.backgroundColor = self.primaryDark;
        tabBar.standardAppearance = appearance;
        if (@available(iOS 15.0, *)) {
            tabBar.scrollEdgeAppearance = appearance;
        }
    }
}

@end
