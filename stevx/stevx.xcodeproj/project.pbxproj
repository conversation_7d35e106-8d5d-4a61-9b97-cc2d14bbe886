// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		DE4208E82E3CCD60003AFD28 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = DE4208E72E3CCD60003AFD28 /* AppDelegate.m */; };
		DE4208EB2E3CCD60003AFD28 /* SceneDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = DE4208EA2E3CCD60003AFD28 /* SceneDelegate.m */; };
		DE4208EE2E3CCD60003AFD28 /* ViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = DE4208ED2E3CCD60003AFD28 /* ViewController.m */; };
		DE4208F12E3CCD60003AFD28 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = DE4208EF2E3CCD60003AFD28 /* Main.storyboard */; };
		DE4208F32E3CCD62003AFD28 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = DE4208F22E3CCD62003AFD28 /* Assets.xcassets */; };
		DE4208F62E3CCD62003AFD28 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = DE4208F42E3CCD62003AFD28 /* LaunchScreen.storyboard */; };
		DE4208F92E3CCD62003AFD28 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = DE4208F82E3CCD62003AFD28 /* main.m */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		DE4208E32E3CCD60003AFD28 /* stevx.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = stevx.app; sourceTree = BUILT_PRODUCTS_DIR; };
		DE4208E62E3CCD60003AFD28 /* AppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		DE4208E72E3CCD60003AFD28 /* AppDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		DE4208E92E3CCD60003AFD28 /* SceneDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SceneDelegate.h; sourceTree = "<group>"; };
		DE4208EA2E3CCD60003AFD28 /* SceneDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SceneDelegate.m; sourceTree = "<group>"; };
		DE4208EC2E3CCD60003AFD28 /* ViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ViewController.h; sourceTree = "<group>"; };
		DE4208ED2E3CCD60003AFD28 /* ViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ViewController.m; sourceTree = "<group>"; };
		DE4208F02E3CCD60003AFD28 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		DE4208F22E3CCD62003AFD28 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		DE4208F52E3CCD62003AFD28 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		DE4208F72E3CCD62003AFD28 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		DE4208F82E3CCD62003AFD28 /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		DE4209022E3CCD63003AFD28 /* stevxTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = stevxTests.m; sourceTree = "<group>"; };
		DE42090C2E3CCD63003AFD28 /* stevxUITests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = stevxUITests.m; sourceTree = "<group>"; };
		DE42090E2E3CCD63003AFD28 /* stevxUITestsLaunchTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = stevxUITestsLaunchTests.m; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		DE4208E02E3CCD60003AFD28 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		DE4208DA2E3CCD60003AFD28 = {
			isa = PBXGroup;
			children = (
				DE4208E52E3CCD60003AFD28 /* stevx */,
				DE4209012E3CCD63003AFD28 /* stevxTests */,
				DE42090B2E3CCD63003AFD28 /* stevxUITests */,
				DE4208E42E3CCD60003AFD28 /* Products */,
			);
			sourceTree = "<group>";
		};
		DE4208E42E3CCD60003AFD28 /* Products */ = {
			isa = PBXGroup;
			children = (
				DE4208E32E3CCD60003AFD28 /* stevx.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		DE4208E52E3CCD60003AFD28 /* stevx */ = {
			isa = PBXGroup;
			children = (
				DE4208E62E3CCD60003AFD28 /* AppDelegate.h */,
				DE4208E72E3CCD60003AFD28 /* AppDelegate.m */,
				DE4208E92E3CCD60003AFD28 /* SceneDelegate.h */,
				DE4208EA2E3CCD60003AFD28 /* SceneDelegate.m */,
				DE4208EC2E3CCD60003AFD28 /* ViewController.h */,
				DE4208ED2E3CCD60003AFD28 /* ViewController.m */,
				DE4208EF2E3CCD60003AFD28 /* Main.storyboard */,
				DE4208F22E3CCD62003AFD28 /* Assets.xcassets */,
				DE4208F42E3CCD62003AFD28 /* LaunchScreen.storyboard */,
				DE4208F72E3CCD62003AFD28 /* Info.plist */,
				DE4208F82E3CCD62003AFD28 /* main.m */,
			);
			path = stevx;
			sourceTree = "<group>";
		};
		DE4209012E3CCD63003AFD28 /* stevxTests */ = {
			isa = PBXGroup;
			children = (
				DE4209022E3CCD63003AFD28 /* stevxTests.m */,
			);
			path = stevxTests;
			sourceTree = "<group>";
		};
		DE42090B2E3CCD63003AFD28 /* stevxUITests */ = {
			isa = PBXGroup;
			children = (
				DE42090C2E3CCD63003AFD28 /* stevxUITests.m */,
				DE42090E2E3CCD63003AFD28 /* stevxUITestsLaunchTests.m */,
			);
			path = stevxUITests;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		DE4208E22E3CCD60003AFD28 /* stevx */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DE4209122E3CCD63003AFD28 /* Build configuration list for PBXNativeTarget "stevx" */;
			buildPhases = (
				DE4208DF2E3CCD60003AFD28 /* Sources */,
				DE4208E02E3CCD60003AFD28 /* Frameworks */,
				DE4208E12E3CCD60003AFD28 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = stevx;
			productName = stevx;
			productReference = DE4208E32E3CCD60003AFD28 /* stevx.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		DE4208DB2E3CCD60003AFD28 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastUpgradeCheck = 1500;
				TargetAttributes = {
					DE4208E22E3CCD60003AFD28 = {
						CreatedOnToolsVersion = 15.0.1;
					};
				};
			};
			buildConfigurationList = DE4208DE2E3CCD60003AFD28 /* Build configuration list for PBXProject "stevx" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = DE4208DA2E3CCD60003AFD28;
			productRefGroup = DE4208E42E3CCD60003AFD28 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				DE4208E22E3CCD60003AFD28 /* stevx */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		DE4208E12E3CCD60003AFD28 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DE4208F62E3CCD62003AFD28 /* LaunchScreen.storyboard in Resources */,
				DE4208F32E3CCD62003AFD28 /* Assets.xcassets in Resources */,
				DE4208F12E3CCD60003AFD28 /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		DE4208DF2E3CCD60003AFD28 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DE4208EE2E3CCD60003AFD28 /* ViewController.m in Sources */,
				DE4208E82E3CCD60003AFD28 /* AppDelegate.m in Sources */,
				DE4208F92E3CCD62003AFD28 /* main.m in Sources */,
				DE4208EB2E3CCD60003AFD28 /* SceneDelegate.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		DE4208EF2E3CCD60003AFD28 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				DE4208F02E3CCD60003AFD28 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		DE4208F42E3CCD62003AFD28 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				DE4208F52E3CCD62003AFD28 /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		DE4209102E3CCD63003AFD28 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		DE4209112E3CCD63003AFD28 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		DE4209132E3CCD63003AFD28 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = stevx/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = test.duckegg.ios.stevx;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		DE4209142E3CCD63003AFD28 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = stevx/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = test.duckegg.ios.stevx;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		DE4208DE2E3CCD60003AFD28 /* Build configuration list for PBXProject "stevx" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DE4209102E3CCD63003AFD28 /* Debug */,
				DE4209112E3CCD63003AFD28 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		DE4209122E3CCD63003AFD28 /* Build configuration list for PBXNativeTarget "stevx" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DE4209132E3CCD63003AFD28 /* Debug */,
				DE4209142E3CCD63003AFD28 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = DE4208DB2E3CCD60003AFD28 /* Project object */;
}
