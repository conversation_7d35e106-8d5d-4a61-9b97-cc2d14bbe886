/// AI灵魂提问数据模型
class SoulQuestion {
  final String id;
  final String question;
  final int reward; // 金币奖励

  const SoulQuestion({
    required this.id,
    required this.question,
    required this.reward,
  });

  static List<SoulQuestion> getAllQuestions() {
    return [
      const SoulQuestion(
        id: 'q1',
        question: '最近哪个作品让你最欣赏？',
        reward: 10,
      ),
      const SoulQuestion(
        id: 'q2',
        question: '你认为蒸汽朋克风格最吸引人的地方是什么？',
        reward: 10,
      ),
      const SoulQuestion(
        id: 'q3',
        question: '在制作珠宝时，你更喜欢哪种材质的质感？',
        reward: 10,
      ),
      const SoulQuestion(
        id: 'q4',
        question: '如果要设计一件代表你个性的饰品，会是什么样子？',
        reward: 10,
      ),
      const SoulQuestion(
        id: 'q5',
        question: '你觉得复古与现代结合的设计有什么魅力？',
        reward: 10,
      ),
      const SoulQuestion(
        id: 'q6',
        question: '在所有蒸汽朋克元素中，你最钟爱哪一个？',
        reward: 10,
      ),
      const SoulQuestion(
        id: 'q7',
        question: '你认为手工制作与机器生产的饰品有什么不同？',
        reward: 10,
      ),
      const SoulQuestion(
        id: 'q8',
        question: '如果能穿越到蒸汽朋克时代，你最想体验什么？',
        reward: 10,
      ),
      const SoulQuestion(
        id: 'q9',
        question: '你觉得齿轮和链条这些元素象征着什么？',
        reward: 10,
      ),
      const SoulQuestion(
        id: 'q10',
        question: '在创作过程中，你更享受设计还是制作的环节？',
        reward: 10,
      ),
    ];
  }
}
