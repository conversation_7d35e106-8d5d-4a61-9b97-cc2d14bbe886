/// AI角色数据模型
class AICharacter {
  final String id;
  final String name;
  final String shortDescription;
  final String longDescription;
  final String iconName;
  final List<String> specialties;
  final List<String> tools;
  final List<String> materials;

  const AICharacter({
    required this.id,
    required this.name,
    required this.shortDescription,
    required this.longDescription,
    required this.iconName,
    required this.specialties,
    required this.tools,
    required this.materials,
  });

  static List<AICharacter> getAllCharacters() {
    return [
      const AICharacter(
        id: 'gear_craft',
        name: '齿轮饰品师',
        shortDescription: 'Make steam punk gear-based jewelry.',
        longDescription: 'Use metal gears (brass, copper), attach with jump rings. Add chains or leather cords. Seal with clear lacquer to prevent tarnish.',
        iconName: 'gear',
        specialties: ['齿轮设计', '金属加工', '防锈处理'],
        tools: ['跳环', '链条', '皮绳'],
        materials: ['黄铜齿轮', '铜制齿轮', '透明漆'],
      ),
      const AICharacter(
        id: 'badge_punk',
        name: '蒸汽徽章师',
        shortDescription: 'Create vintage steam punk badges.',
        longDescription: 'Cut metal sheets into shapes, stamp with gears/rivets. Add enamel accents (burgundy, gold). Attach pins; distress edges for aged look.',
        iconName: 'badge',
        specialties: ['徽章制作', '金属冲压', '做旧工艺'],
        tools: ['金属片', '冲压工具', '别针'],
        materials: ['金属板材', '珐琅颜料', '酒红色涂料'],
      ),
      const AICharacter(
        id: 'goggle_craft',
        name: '护目镜饰品师',
        shortDescription: 'Make mini steam punk goggle accessories.',
        longDescription: 'Use small metal frames, add tinted plastic lenses. Attach leather straps, tiny gears. Seal with antique brass spray.',
        iconName: 'goggles',
        specialties: ['护目镜制作', '镜片处理', '皮革工艺'],
        tools: ['小型金属框架', '有色塑料镜片', '皮革带'],
        materials: ['金属框架', '着色镜片', '古铜喷漆'],
      ),
      const AICharacter(
        id: 'key_punk',
        name: '钥匙挂件师',
        shortDescription: 'Craft steam punk keychains/pendants.',
        longDescription: 'Combine old keys with gears, use wire to wrap. Add beads (amber, black). Seal with wax for a vintage finish.',
        iconName: 'key',
        specialties: ['钥匙改造', '线材缠绕', '复古处理'],
        tools: ['旧钥匙', '金属线', '珠子'],
        materials: ['古董钥匙', '琥珀珠', '黑色珠子', '蜡封剂'],
      ),
      const AICharacter(
        id: 'start_punk',
        name: '初学者朋克师',
        shortDescription: 'Basics for steam punk jewelry newbies.',
        longDescription: 'Start with pre-made gears, use hot glue for ease. Try simple designs (gear necklaces). Practice wire wrapping with pliers.',
        iconName: 'beginner',
        specialties: ['基础教学', '简单设计', '工具使用'],
        tools: ['预制齿轮', '热熔胶', '钳子'],
        materials: ['成品齿轮', '胶水', '基础线材'],
      ),
      const AICharacter(
        id: 'leather_punk',
        name: '皮革朋克师',
        shortDescription: 'Make leather steam punk accessories.',
        longDescription: 'Stamp leather with gears, add metal studs. Dye brown/black, distress edges. Attach buckles or chains.',
        iconName: 'leather',
        specialties: ['皮革工艺', '印花技术', '金属装饰'],
        tools: ['皮革印章', '金属铆钉', '扣环'],
        materials: ['皮革', '棕色染料', '黑色染料'],
      ),
      const AICharacter(
        id: 'bend_punk',
        name: '金属弯曲师',
        shortDescription: 'Shape metal for steam punk pieces.',
        longDescription: 'Use aluminum wire (easy to bend) for frames. Form loops, attach gears. Use pliers for precise curves.',
        iconName: 'bend',
        specialties: ['金属塑形', '线材弯曲', '精密加工'],
        tools: ['铝线', '钳子', '成型工具'],
        materials: ['铝制线材', '弯曲模具', '固定夹具'],
      ),
      const AICharacter(
        id: 'ring_punk',
        name: '蒸汽戒指师',
        shortDescription: 'Craft steam punk style rings.',
        longDescription: 'Use adjustable ring bases, glue small gears. Add tiny chains or mini keys. Seal to protect from wear.',
        iconName: 'ring',
        specialties: ['戒指制作', '微型装饰', '耐磨处理'],
        tools: ['可调戒指底座', '小型齿轮', '迷你钥匙'],
        materials: ['戒指基座', '微型齿轮', '细链条'],
      ),
      const AICharacter(
        id: 'aged_punk',
        name: '饰品做旧师',
        shortDescription: 'Distress steam punk jewelry for vintage look.',
        longDescription: 'Use sandpaper on metal edges, apply patina solution. Rub with wax to highlight details. Avoid over-distressing.',
        iconName: 'aged',
        specialties: ['做旧工艺', '表面处理', '细节强化'],
        tools: ['砂纸', '铜绿溶液', '蜡'],
        materials: ['磨砂材料', '做旧溶液', '保护蜡'],
      ),
      const AICharacter(
        id: 'mix_punk',
        name: '材料搭配师',
        shortDescription: 'Combine materials for steam punk pieces.',
        longDescription: 'Pair metal (brass) with leather, wood, or glass. Balance textures—smooth metal with rough leather. Keep color schemes warm (bronze, brown).',
        iconName: 'mix',
        specialties: ['材料搭配', '质感平衡', '色彩协调'],
        tools: ['黄铜', '皮革', '木材', '玻璃'],
        materials: ['多种金属', '天然材料', '装饰玻璃'],
      ),
    ];
  }
}
