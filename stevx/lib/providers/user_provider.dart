import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/soul_question.dart';

/// 用户数据管理
class UserProvider with ChangeNotifier {
  int _coins = 0;
  String? _lastQuestionDate;
  String? _todayQuestionId;
  bool _hasAnsweredToday = false;
  bool _isFirstTime = true;

  int get coins => _coins;
  bool get hasAnsweredToday => _hasAnsweredToday;
  bool get isFirstTime => _isFirstTime;
  String? get todayQuestionId => _todayQuestionId;

  /// 初始化用户数据
  Future<void> initializeUser() async {
    final prefs = await SharedPreferences.getInstance();
    _coins = prefs.getInt('coins') ?? 0;
    _lastQuestionDate = prefs.getString('lastQuestionDate');
    _todayQuestionId = prefs.getString('todayQuestionId');
    _isFirstTime = prefs.getBool('isFirstTime') ?? true;
    
    // 检查是否是新的一天
    final today = DateTime.now().toIso8601String().split('T')[0];
    if (_lastQuestionDate != today) {
      _hasAnsweredToday = false;
      _todayQuestionId = null;
    } else {
      _hasAnsweredToday = prefs.getBool('hasAnsweredToday') ?? false;
    }
    
    notifyListeners();
  }

  /// 获取今日问题
  SoulQuestion? getTodayQuestion() {
    if (_todayQuestionId == null) {
      // 生成今日问题
      final questions = SoulQuestion.getAllQuestions();
      final today = DateTime.now().toIso8601String().split('T')[0];
      final random = DateTime.now().day % questions.length;
      final question = questions[random];
      _todayQuestionId = question.id;
      _saveTodayQuestion(question.id);
      return question;
    } else {
      // 返回已存储的今日问题
      final questions = SoulQuestion.getAllQuestions();
      return questions.firstWhere((q) => q.id == _todayQuestionId);
    }
  }

  /// 回答问题并获得奖励
  Future<void> answerQuestion(String answer) async {
    if (_hasAnsweredToday) return;
    
    final question = getTodayQuestion();
    if (question != null) {
      _coins += question.reward;
      _hasAnsweredToday = true;
      
      final prefs = await SharedPreferences.getInstance();
      final today = DateTime.now().toIso8601String().split('T')[0];
      
      await prefs.setInt('coins', _coins);
      await prefs.setBool('hasAnsweredToday', true);
      await prefs.setString('lastQuestionDate', today);
      await prefs.setString('userAnswer', answer);
      
      notifyListeners();
    }
  }

  /// 标记不是第一次使用
  Future<void> markNotFirstTime() async {
    _isFirstTime = false;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('isFirstTime', false);
    notifyListeners();
  }

  /// 保存今日问题ID
  Future<void> _saveTodayQuestion(String questionId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('todayQuestionId', questionId);
  }
}
