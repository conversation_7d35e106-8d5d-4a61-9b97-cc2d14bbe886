import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../core/theme/app_colors.dart';
import '../providers/user_provider.dart';
import '../models/soul_question.dart';

/// AI灵魂提问组件
class SoulQuestionWidget extends StatefulWidget {
  const SoulQuestionWidget({super.key});

  @override
  State<SoulQuestionWidget> createState() => _SoulQuestionWidgetState();
}

class _SoulQuestionWidgetState extends State<SoulQuestionWidget> {
  final TextEditingController _answerController = TextEditingController();
  bool _showAnswerInput = false;

  @override
  void dispose() {
    _answerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<UserProvider>(
      builder: (context, userProvider, child) {
        // 首次使用显示引导弹窗
        if (userProvider.isFirstTime) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _showGuideDialog(context, userProvider);
          });
        }

        return Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(20),
          decoration: const BoxDecoration(
            color: AppColors.accent,
            borderRadius: BorderRadius.all(Radius.circular(16)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题区域
              Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: const BoxDecoration(
                      color: AppColors.brass,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.psychology,
                      color: AppColors.textWhite,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'AI 灵魂提问',
                      style: TextStyle(
                        color: AppColors.textWhite,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              
              // 问题内容
              if (userProvider.hasAnsweredToday)
                _buildAnsweredState()
              else
                _buildQuestionState(userProvider),
            ],
          ),
        );
      },
    );
  }

  /// 已回答状态
  Widget _buildAnsweredState() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.textWhite.withOpacity(0.1),
        borderRadius: const BorderRadius.all(Radius.circular(12)),
      ),
      child: const Column(
        children: [
          Icon(
            Icons.check_circle,
            color: AppColors.success,
            size: 48,
          ),
          SizedBox(height: 12),
          Text(
            '今日问题已完成！',
            style: TextStyle(
              color: AppColors.textWhite,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8),
          Text(
            '明天再来获取新的灵魂问题吧',
            style: TextStyle(
              color: AppColors.textWhite,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  /// 问题状态
  Widget _buildQuestionState(UserProvider userProvider) {
    final question = userProvider.getTodayQuestion();
    if (question == null) return const SizedBox();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 问题文本
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.textWhite.withOpacity(0.1),
            borderRadius: const BorderRadius.all(Radius.circular(12)),
          ),
          child: Text(
            question.question,
            style: const TextStyle(
              color: AppColors.textWhite,
              fontSize: 16,
            ),
          ),
        ),
        const SizedBox(height: 16),
        
        // 奖励信息
        Row(
          children: [
            const Icon(
              Icons.monetization_on,
              color: AppColors.brass,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              '回答可获得 ${question.reward} 金币',
              style: const TextStyle(
                color: AppColors.textWhite,
                fontSize: 14,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        
        // 回答按钮或输入框
        if (!_showAnswerInput)
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                setState(() {
                  _showAnswerInput = true;
                });
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.brass,
                foregroundColor: AppColors.textWhite,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('开始回答'),
            ),
          )
        else
          _buildAnswerInput(userProvider, question),
      ],
    );
  }

  /// 回答输入区域
  Widget _buildAnswerInput(UserProvider userProvider, SoulQuestion question) {
    return Column(
      children: [
        TextField(
          controller: _answerController,
          maxLines: 3,
          style: const TextStyle(color: AppColors.textWhite),
          decoration: InputDecoration(
            hintText: '分享你的想法...',
            hintStyle: TextStyle(color: AppColors.textWhite.withOpacity(0.6)),
            filled: true,
            fillColor: AppColors.textWhite.withOpacity(0.1),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide.none,
            ),
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: ElevatedButton(
                onPressed: () async {
                  if (_answerController.text.trim().isNotEmpty) {
                    await userProvider.answerQuestion(_answerController.text.trim());
                    setState(() {
                      _showAnswerInput = false;
                    });
                    _showRewardDialog(context, question.reward);
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.brass,
                  foregroundColor: AppColors.textWhite,
                ),
                child: const Text('提交答案'),
              ),
            ),
            const SizedBox(width: 12),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _showAnswerInput = false;
                  _answerController.clear();
                });
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.textWhite.withOpacity(0.2),
                foregroundColor: AppColors.textWhite,
              ),
              child: const Text('取消'),
            ),
          ],
        ),
      ],
    );
  }

  /// 显示引导弹窗
  void _showGuideDialog(BuildContext context, UserProvider userProvider) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.background,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: const Row(
          children: [
            Icon(Icons.psychology, color: AppColors.brass),
            SizedBox(width: 8),
            Text(
              'AI 灵魂提问',
              style: TextStyle(color: AppColors.textDark),
            ),
          ],
        ),
        content: const Text(
          '每天你都可以获得一个随机的灵魂问题，回答后可获得10金币奖励。这些问题将帮助你更好地理解蒸汽朋克文化和珠宝制作的艺术。',
          style: TextStyle(color: AppColors.textDark),
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              userProvider.markNotFirstTime();
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.brass,
              foregroundColor: AppColors.textWhite,
            ),
            child: const Text('我知道了'),
          ),
        ],
      ),
    );
  }

  /// 显示奖励弹窗
  void _showRewardDialog(BuildContext context, int reward) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.background,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: const Row(
          children: [
            Icon(Icons.celebration, color: AppColors.success),
            SizedBox(width: 8),
            Text(
              '恭喜！',
              style: TextStyle(color: AppColors.textDark),
            ),
          ],
        ),
        content: Text(
          '你获得了 $reward 金币奖励！',
          style: const TextStyle(color: AppColors.textDark),
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.brass,
              foregroundColor: AppColors.textWhite,
            ),
            child: const Text('太棒了'),
          ),
        ],
      ),
    );
  }
}
