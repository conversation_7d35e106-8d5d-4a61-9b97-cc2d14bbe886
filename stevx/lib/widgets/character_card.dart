import 'package:flutter/material.dart';
import '../core/theme/app_colors.dart';
import '../models/ai_character.dart';

/// AI角色卡片组件
class CharacterCard extends StatelessWidget {
  final AICharacter character;
  final VoidCallback? onTap;

  const CharacterCard({
    super.key,
    required this.character,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: const BoxDecoration(
          color: AppColors.primaryDark,
          borderRadius: BorderRadius.all(Radius.circular(12)),
        ),
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 角色图标
            Container(
              width: 48,
              height: 48,
              decoration: const BoxDecoration(
                color: AppColors.brass,
                shape: BoxShape.circle,
              ),
              child: Icon(
                _getIconData(character.iconName),
                color: AppColors.textWhite,
                size: 24,
              ),
            ),
            const SizedBox(height: 12),
            
            // 角色名称
            Text(
              character.name,
              style: const TextStyle(
                color: AppColors.textWhite,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 8),
            
            // 简短描述
            Text(
              character.shortDescription,
              style: const TextStyle(
                color: AppColors.textWhite,
                fontSize: 11,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  /// 根据图标名称返回对应的图标数据
  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'gear':
        return Icons.settings;
      case 'badge':
        return Icons.military_tech;
      case 'goggles':
        return Icons.visibility;
      case 'key':
        return Icons.vpn_key;
      case 'beginner':
        return Icons.school;
      case 'leather':
        return Icons.texture;
      case 'bend':
        return Icons.architecture;
      case 'ring':
        return Icons.radio_button_unchecked;
      case 'aged':
        return Icons.auto_fix_high;
      case 'mix':
        return Icons.palette;
      default:
        return Icons.build;
    }
  }
}
