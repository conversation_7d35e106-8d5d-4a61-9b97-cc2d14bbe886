import 'package:flutter/material.dart';

/// 蒸汽朋克主题配色方案
class AppColors {
  // 主色调（深色背景）
  static const Color primaryDark = Color(0xFF2C2C2C);
  
  // 辅助色（黄铜色）
  static const Color brass = Color(0xFFC58B43);
  
  // 点缀色（酒红色）
  static const Color accent = Color(0xFF8B0000);
  
  // 背景色（旧纸张白）
  static const Color background = Color(0xFFF7F2E8);
  
  // 文本色（纯白）
  static const Color textWhite = Color(0xFFFFFFFF);
  
  // 文本色（深色）
  static const Color textDark = Color(0xFF2C2C2C);
  
  // 半透明遮罩
  static const Color overlay = Color(0x80000000);
  
  // 成功色（用于奖励提示）
  static const Color success = Color(0xFF4CAF50);
  
  // 警告色
  static const Color warning = Color(0xFFFF9800);
}
