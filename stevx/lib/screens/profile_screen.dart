import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../core/theme/app_colors.dart';
import '../providers/user_provider.dart';
import '../widgets/soul_question_widget.dart';

/// 我的页面
class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text(
          '我的',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: AppColors.textWhite,
          ),
        ),
        backgroundColor: AppColors.primaryDark,
        elevation: 0,
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // 用户信息卡片
            _buildUserInfoCard(),
            
            // AI灵魂提问模块
            const SoulQuestionWidget(),
            
            // 其他功能模块
            _buildFeatureCards(),
          ],
        ),
      ),
    );
  }

  /// 用户信息卡片
  Widget _buildUserInfoCard() {
    return Consumer<UserProvider>(
      builder: (context, userProvider, child) {
        return Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(20),
          decoration: const BoxDecoration(
            color: AppColors.primaryDark,
            borderRadius: BorderRadius.all(Radius.circular(16)),
          ),
          child: Row(
            children: [
              // 头像
              Container(
                width: 60,
                height: 60,
                decoration: const BoxDecoration(
                  color: AppColors.brass,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.person,
                  color: AppColors.textWhite,
                  size: 30,
                ),
              ),
              const SizedBox(width: 16),
              
              // 用户信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Steampunk Crafter',
                      style: TextStyle(
                        color: AppColors.textWhite,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        const Icon(
                          Icons.monetization_on,
                          color: AppColors.brass,
                          size: 16,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${userProvider.coins} 金币',
                          style: const TextStyle(
                            color: AppColors.textWhite,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 功能卡片
  Widget _buildFeatureCards() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          // 我的作品
          _buildFeatureCard(
            icon: Icons.photo_library,
            title: '我的作品',
            subtitle: '查看你创作的蒸汽朋克珠宝',
            onTap: () {
              // TODO: 导航到作品页面
            },
          ),
          const SizedBox(height: 12),
          
          // 收藏夹
          _buildFeatureCard(
            icon: Icons.favorite,
            title: '收藏夹',
            subtitle: '保存喜欢的设计和教程',
            onTap: () {
              // TODO: 导航到收藏页面
            },
          ),
          const SizedBox(height: 12),
          
          // 学习记录
          _buildFeatureCard(
            icon: Icons.school,
            title: '学习记录',
            subtitle: '查看你的学习进度和成就',
            onTap: () {
              // TODO: 导航到学习记录页面
            },
          ),
          const SizedBox(height: 12),
          
          // 设置
          _buildFeatureCard(
            icon: Icons.settings,
            title: '设置',
            subtitle: '个性化你的应用体验',
            onTap: () {
              // TODO: 导航到设置页面
            },
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  /// 单个功能卡片
  Widget _buildFeatureCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: const BoxDecoration(
          color: AppColors.primaryDark,
          borderRadius: BorderRadius.all(Radius.circular(12)),
        ),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: const BoxDecoration(
                color: AppColors.brass,
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                color: AppColors.textWhite,
                size: 20,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      color: AppColors.textWhite,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      color: AppColors.textWhite.withOpacity(0.7),
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
            const Icon(
              Icons.arrow_forward_ios,
              color: AppColors.brass,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }
}
