import 'package:flutter/material.dart';
import '../core/theme/app_colors.dart';
import '../models/ai_character.dart';

/// AI角色详情页面
class CharacterDetailScreen extends StatefulWidget {
  final AICharacter character;

  const CharacterDetailScreen({
    super.key,
    required this.character,
  });

  @override
  State<CharacterDetailScreen> createState() => _CharacterDetailScreenState();
}

class _CharacterDetailScreenState extends State<CharacterDetailScreen> {
  final TextEditingController _messageController = TextEditingController();
  final List<ChatMessage> _messages = [];

  @override
  void initState() {
    super.initState();
    // 添加欢迎消息
    _messages.add(ChatMessage(
      text: '你好！我是${widget.character.name}，${widget.character.longDescription}',
      isUser: false,
      timestamp: DateTime.now(),
    ));
  }

  @override
  void dispose() {
    _messageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(
          widget.character.name,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.textWhite,
          ),
        ),
        backgroundColor: AppColors.primaryDark,
        elevation: 0,
        iconTheme: const IconThemeData(color: AppColors.textWhite),
      ),
      body: Column(
        children: [
          // 角色信息卡片
          _buildCharacterInfo(),
          
          // 聊天区域
          Expanded(
            child: _buildChatArea(),
          ),
          
          // 输入区域
          _buildInputArea(),
        ],
      ),
    );
  }

  /// 角色信息卡片
  Widget _buildCharacterInfo() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: AppColors.primaryDark,
        borderRadius: BorderRadius.all(Radius.circular(16)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              // 角色图标
              Container(
                width: 60,
                height: 60,
                decoration: const BoxDecoration(
                  color: AppColors.brass,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  _getIconData(widget.character.iconName),
                  color: AppColors.textWhite,
                  size: 30,
                ),
              ),
              const SizedBox(width: 16),
              
              // 角色名称和简介
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.character.name,
                      style: const TextStyle(
                        color: AppColors.textWhite,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      widget.character.shortDescription,
                      style: TextStyle(
                        color: AppColors.textWhite.withOpacity(0.8),
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // 专长标签
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: widget.character.specialties.map((specialty) {
              return Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: AppColors.brass.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: AppColors.brass, width: 1),
                ),
                child: Text(
                  specialty,
                  style: const TextStyle(
                    color: AppColors.brass,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  /// 聊天区域
  Widget _buildChatArea() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: const BoxDecoration(
        color: AppColors.primaryDark,
        borderRadius: BorderRadius.all(Radius.circular(16)),
      ),
      child: Column(
        children: [
          // 聊天标题
          Container(
            padding: const EdgeInsets.all(16),
            child: const Row(
              children: [
                Icon(Icons.chat, color: AppColors.brass, size: 20),
                SizedBox(width: 8),
                Text(
                  '与AI专家对话',
                  style: TextStyle(
                    color: AppColors.textWhite,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          
          // 消息列表
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _messages.length,
              itemBuilder: (context, index) {
                final message = _messages[index];
                return _buildMessageBubble(message);
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 消息气泡
  Widget _buildMessageBubble(ChatMessage message) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment: message.isUser 
            ? MainAxisAlignment.end 
            : MainAxisAlignment.start,
        children: [
          if (!message.isUser) ...[
            Container(
              width: 32,
              height: 32,
              decoration: const BoxDecoration(
                color: AppColors.brass,
                shape: BoxShape.circle,
              ),
              child: Icon(
                _getIconData(widget.character.iconName),
                color: AppColors.textWhite,
                size: 16,
              ),
            ),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: message.isUser 
                    ? AppColors.brass 
                    : AppColors.textWhite.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                message.text,
                style: const TextStyle(
                  color: AppColors.textWhite,
                  fontSize: 14,
                ),
              ),
            ),
          ),
          if (message.isUser) ...[
            const SizedBox(width: 8),
            Container(
              width: 32,
              height: 32,
              decoration: const BoxDecoration(
                color: AppColors.accent,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.person,
                color: AppColors.textWhite,
                size: 16,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 输入区域
  Widget _buildInputArea() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: AppColors.primaryDark,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _messageController,
              style: const TextStyle(color: AppColors.textWhite),
              decoration: InputDecoration(
                hintText: '输入你的问题...',
                hintStyle: TextStyle(color: AppColors.textWhite.withOpacity(0.6)),
                filled: true,
                fillColor: AppColors.textWhite.withOpacity(0.1),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                  borderSide: BorderSide.none,
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          GestureDetector(
            onTap: _sendMessage,
            child: Container(
              width: 48,
              height: 48,
              decoration: const BoxDecoration(
                color: AppColors.brass,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.send,
                color: AppColors.textWhite,
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 发送消息
  void _sendMessage() {
    final text = _messageController.text.trim();
    if (text.isEmpty) return;

    setState(() {
      // 添加用户消息
      _messages.add(ChatMessage(
        text: text,
        isUser: true,
        timestamp: DateTime.now(),
      ));

      // 模拟AI回复
      _messages.add(ChatMessage(
        text: _generateAIResponse(text),
        isUser: false,
        timestamp: DateTime.now(),
      ));
    });

    _messageController.clear();
  }

  /// 生成AI回复（简单模拟）
  String _generateAIResponse(String userMessage) {
    final responses = [
      '这是一个很好的问题！关于${widget.character.name}的专长，我建议你可以从${widget.character.materials.first}开始尝试。',
      '根据我的经验，${widget.character.specialties.first}是关键。你可以使用${widget.character.tools.first}来实现这个效果。',
      '让我为你详细解释一下这个工艺流程...',
      '这个技巧需要一些练习，但是一旦掌握了，你就能创造出令人惊艳的作品！',
    ];
    
    return responses[DateTime.now().millisecond % responses.length];
  }

  /// 根据图标名称返回对应的图标数据
  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'gear':
        return Icons.settings;
      case 'badge':
        return Icons.military_tech;
      case 'goggles':
        return Icons.visibility;
      case 'key':
        return Icons.vpn_key;
      case 'beginner':
        return Icons.school;
      case 'leather':
        return Icons.texture;
      case 'bend':
        return Icons.architecture;
      case 'ring':
        return Icons.radio_button_unchecked;
      case 'aged':
        return Icons.auto_fix_high;
      case 'mix':
        return Icons.palette;
      default:
        return Icons.build;
    }
  }
}

/// 聊天消息模型
class ChatMessage {
  final String text;
  final bool isUser;
  final DateTime timestamp;

  ChatMessage({
    required this.text,
    required this.isUser,
    required this.timestamp,
  });
}
